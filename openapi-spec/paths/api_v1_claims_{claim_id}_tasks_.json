{"post": {"tags": ["claims", "tasks"], "summary": "Create Task for C<PERSON>m", "description": "Create a new task associated with a specific claim. Requires 'CREATE_TASK' permission.", "operationId": "create_task_api_v1_claims__claim_id__tasks__post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "description": "The ID or Number of the claim to associate the task with.", "title": "Claim Identifier"}, "description": "The ID or Number of the claim to associate the task with."}, {"name": "claim_identifier", "in": "query", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["client", "gl_details", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskCreateRequestBody.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/TaskRead.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}