{"get": {"tags": ["claims", "audit", "audit"], "summary": "List Audit Entries", "description": "Get audit trail entries for a claim with optional filtering and pagination.", "operationId": "list_audit_entries_api_v1_claims__claim_identifier__audit_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "entity_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/EntityType.json"}, {"type": "null"}], "description": "Filter by entity type", "title": "Entity Type"}, "description": "Filter by entity type"}, {"name": "change_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/ChangeType.json"}, {"type": "null"}], "description": "Filter by change type", "title": "Change Type"}, "description": "Filter by change type"}, {"name": "from_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by date range start", "title": "From Date"}, "description": "Filter by date range start"}, {"name": "to_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "description": "Filter by date range end", "title": "To Date"}, "description": "Filter by date range end"}, {"name": "changed_by_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by user who made the change", "title": "Changed By Id"}, "description": "Filter by user who made the change"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "description": "Number of items to skip (pagination)", "default": 0, "title": "<PERSON><PERSON>"}, "description": "Number of items to skip (pagination)"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "description": "Maximum number of items to return (pagination)", "default": 50, "title": "Limit"}, "description": "Maximum number of items to return (pagination)"}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["client", "gl_details", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/PaginatedAuditTrailResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["claims", "audit", "audit"], "summary": "Create Audit Entry", "description": "Create a manual audit entry.\n\nThis is typically used for administrative purposes or to document\nactions that occurred outside the system.", "operationId": "create_audit_entry_api_v1_claims__claim_identifier__audit_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["client", "gl_details", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/AuditTrailCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/AuditTrailResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}