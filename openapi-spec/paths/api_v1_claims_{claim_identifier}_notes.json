{"post": {"tags": ["notes", "notes"], "summary": "Create Note For Claim", "description": "Create a new note associated with a specific claim (identified by UUID or Number).", "operationId": "create_note_for_claim_api_v1_claims__claim_identifier__notes_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["client", "gl_details", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/NoteCreateRequestBody.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/NoteResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}