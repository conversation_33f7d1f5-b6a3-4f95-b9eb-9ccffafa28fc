{"get": {"tags": ["fnols", "fnols"], "summary": "Get Fnol By Number", "description": "Get FNOL by number.", "operationId": "get_fnol_by_number_api_v1_fnols_number__fnol_number__get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "fnol_number", "in": "path", "required": true, "schema": {"type": "string", "title": "Fnol Number"}}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (client, claims)", "default": ["client", "claims"], "title": "Include"}, "description": "Include related items (client, claims)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/FNOLResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}