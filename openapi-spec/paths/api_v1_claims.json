{"get": {"tags": ["claims", "claims"], "summary": "List Claims", "description": "List claims with filtering based on user's permissions.", "operationId": "list_claims_api_v1_claims_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/ClaimStatus.json"}, {"type": "null"}], "title": "Status"}}, {"name": "type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "../components/schemas/ClaimType.json"}, {"type": "null"}], "title": "Type"}}, {"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by client ID", "title": "Client Id"}, "description": "Filter by client ID"}, {"name": "assigned_to_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Assigned To Id"}}, {"name": "supervisor_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Supervisor Id"}}, {"name": "team", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Team"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search across claim number, claimant name, and description", "title": "Search"}, "description": "Search across claim number, claimant name, and description"}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["customer"], "title": "Include"}, "description": "Include related items (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/PaginatedClaimResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["claims", "claims"], "summary": "Create <PERSON><PERSON><PERSON>", "description": "Create a new claim.", "operationId": "create_claim_api_v1_claims_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "claim_type", "in": "query", "required": true, "schema": {"$ref": "../components/schemas/ClaimType.json", "description": "Type of claim to create"}, "description": "Type of claim to create"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "additionalProperties": true, "title": "Claim Data"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"anyOf": [{"$ref": "../components/schemas/AutoClaimResponse.json"}, {"$ref": "../components/schemas/PropertyClaimResponse.json"}, {"$ref": "../components/schemas/GeneralLiabilityClaimResponse.json"}], "title": "Response Create Claim Api V1 Claims Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}