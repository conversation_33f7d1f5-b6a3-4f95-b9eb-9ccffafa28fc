{"get": {"tags": ["reports", "reports"], "summary": "Get Claims By Type Report", "description": "Get Claims by Type Report.\n\nBreaks down claims by their type (AUTO, PROPERTY, GENERAL_LIABILITY, etc.)\nand provides count and percentage for each type.\n\nThe report can be filtered by client_id or user_id.", "operationId": "get_claims_by_type_report_api_v1_reports_claims_by_type_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Time period for report", "title": "Period"}, "description": "Time period for report"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date (ISO format)", "title": "Start Date"}, "description": "Start date (ISO format)"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date (ISO format)", "title": "End Date"}, "description": "End date (ISO format)"}, {"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by client ID", "title": "Client Id"}, "description": "Filter by client ID"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ReportResponseClaimsByType.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}