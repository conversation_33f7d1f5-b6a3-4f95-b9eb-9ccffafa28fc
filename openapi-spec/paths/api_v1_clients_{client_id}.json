{"get": {"tags": ["clients", "clients"], "summary": "Get Client", "description": "Get a client by ID.", "operationId": "get_client_api_v1_clients__client_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["clients", "clients"], "summary": "Update Client", "description": "Update a client.", "operationId": "update_client_api_v1_clients__client_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Client Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["clients", "clients"], "summary": "Delete Client", "description": "Delete a client.", "operationId": "delete_client_api_v1_clients__client_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Client Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}