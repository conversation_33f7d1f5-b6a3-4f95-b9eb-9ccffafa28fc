{"get": {"tags": ["config", "config"], "summary": "Get Client Authority Threshold", "description": "Get a specific client authority threshold. Permissions are checked at the service layer.", "operationId": "get_client_authority_threshold_api_v1_config_authority__threshold_id__get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "threshold_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON><PERSON> Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientAuthorityThresholdInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "patch": {"tags": ["config", "config"], "summary": "Update Client Authority Threshold", "description": "Update a client authority threshold. Permissions are checked at the service layer.", "operationId": "update_client_authority_threshold_api_v1_config_authority__threshold_id__patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "threshold_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON><PERSON> Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientAuthorityThresholdUpdate.json"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientAuthorityThresholdInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "delete": {"tags": ["config", "config"], "summary": "Delete Client Authority Threshold", "description": "Delete a client authority threshold. Permissions are checked at the service layer.", "operationId": "delete_client_authority_threshold_api_v1_config_authority__threshold_id__delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "threshold_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "<PERSON><PERSON><PERSON><PERSON> Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}