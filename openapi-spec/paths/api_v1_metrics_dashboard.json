{"get": {"tags": ["metrics", "metrics"], "summary": "Get Dashboard Metrics", "description": "Get dashboard metrics.\n\nRetrieves key metrics for the dashboard, such as:\n- Total claims\n- Open claims\n- New claims in period\n- Closed claims in period\n- Average claim lifecycle\n- Total payments\n- Total reserves\n- Tasks pending\n- Tasks overdue\n- FNOLs pending\n\nThe metrics can be filtered by client_id or user_id, and compared\nwith the previous period of equal length.", "operationId": "get_dashboard_metrics_api_v1_metrics_dashboard_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "period", "in": "query", "required": false, "schema": {"type": "string", "description": "Time period for metrics", "default": "last_30_days", "title": "Period"}, "description": "Time period for metrics"}, {"name": "compare_period", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Compare with previous period", "default": true, "title": "Compare Period"}, "description": "Compare with previous period"}, {"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by client ID", "title": "Client Id"}, "description": "Filter by client ID"}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by user ID", "title": "User Id"}, "description": "Filter by user ID"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/DashboardMetricsResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}