{"get": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Get Injuries", "description": "Get all injuries for an injured person.", "operationId": "get_injuries_api_v1_claims__claim_identifier__injured_persons__person_id__injuries_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["client", "gl_details", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/InjuryResponse.json"}, "title": "Response Get Injuries Api V1 Claims  Claim Identifier  Injured Persons  Person Id  Injuries Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["claims", "claims", "bodily-injury"], "summary": "Create Injury", "description": "Create an injury for an injured person.", "operationId": "create_injury_api_v1_claims__claim_identifier__injured_persons__person_id__injuries_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "person_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "description": "Injured Person ID", "title": "Person Id"}, "description": "Injured Person ID"}, {"name": "claim_identifier", "in": "path", "required": true, "schema": {"type": "string", "title": "Claim Identifier"}}, {"name": "includes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)", "default": ["client", "gl_details", "bodily_injury_details", "premises_details"], "title": "Includes"}, "description": "Optional includes for claim data (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/InjuryCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/InjuryResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}