{"get": {"tags": ["fnols", "fnols"], "summary": "List Fnols", "description": "List FNOLs with filtering based on user's permissions.", "operationId": "list_fnols_api_v1_fnols_get", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "client_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "description": "Filter by client ID", "title": "Client Id"}, "description": "Filter by client ID"}, {"name": "include", "in": "query", "required": false, "schema": {"anyOf": [{"type": "array", "uniqueItems": true, "items": {"type": "string"}}, {"type": "null"}], "description": "Include related items (client, claims)", "default": ["client"], "title": "Include"}, "description": "Include related items (client, claims)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/FNOLResponse.json"}, "title": "Response List Fnols Api V1 Fnols Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["fnols", "fnols"], "summary": "Create Fnol", "description": "Create a new FNOL.", "operationId": "create_fnol_api_v1_fnols_post", "security": [{"OAuth2PasswordBearer": []}, {"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/FNOLCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/FNOLResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}