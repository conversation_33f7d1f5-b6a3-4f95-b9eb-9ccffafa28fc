{"get": {"tags": ["clients", "clients"], "summary": "List Clients", "description": "List all clients.", "operationId": "list_clients_api_v1_clients_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "active_only", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Active Only"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/ClientResponse.json"}, "title": "Response List Clients Api V1 Clients Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["clients", "clients"], "summary": "Create Client", "description": "Create a new client.", "operationId": "create_client_api_v1_clients_post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientResponse.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}