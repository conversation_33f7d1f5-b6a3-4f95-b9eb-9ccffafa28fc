{"get": {"tags": ["config", "config"], "summary": "List Client Authority Thresholds", "description": "List authority thresholds for a specific client. Permissions are checked at the service layer.", "operationId": "list_client_authority_thresholds_api_v1_config_clients__client_id__authority_get", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Client Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "../components/schemas/ClientAuthorityThresholdInDB.json"}, "title": "Response List Client Authority Thresholds Api V1 Config Clients  Client Id  Authority Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}, "post": {"tags": ["config", "config"], "summary": "Create Client Authority Threshold", "description": "Create a new authority threshold for a client. Permissions are checked at the service layer.", "operationId": "create_client_authority_threshold_api_v1_config_clients__client_id__authority_post", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "client_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Client Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientAuthorityThresholdCreate.json"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "../components/schemas/ClientAuthorityThresholdInDB.json"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "../components/schemas/HTTPValidationError.json"}}}}}}}