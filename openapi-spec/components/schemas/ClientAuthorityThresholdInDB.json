{"properties": {"authority_role": {"$ref": "./AuthorityRole.json"}, "reserve_limit": {"type": "string", "title": "Reserve Limit"}, "payment_limit": {"type": "string", "title": "Payment Limit"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "client_id": {"type": "string", "format": "uuid", "title": "Client Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "is_deleted": {"type": "boolean", "title": "Is Deleted"}}, "type": "object", "required": ["authority_role", "reserve_limit", "payment_limit", "id", "client_id", "created_at", "updated_at", "is_deleted"], "title": "ClientAuthorityThresholdInDB", "description": "Schema for client-specific authority thresholds in database."}