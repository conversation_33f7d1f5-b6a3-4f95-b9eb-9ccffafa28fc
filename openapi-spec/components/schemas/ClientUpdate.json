{"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name", "description": "Name of the client/insurance company"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Additional details about the client"}, "active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Active", "description": "Whether the client is active in the system"}}, "type": "object", "title": "ClientUpdate", "description": "Update client schema."}