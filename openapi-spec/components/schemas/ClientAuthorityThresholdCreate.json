{"properties": {"authority_role": {"$ref": "./AuthorityRole.json"}, "reserve_limit": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Reserve Limit"}, "payment_limit": {"anyOf": [{"type": "number"}, {"type": "string"}], "title": "Payment Limit"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "client_id": {"type": "string", "format": "uuid", "title": "Client Id"}}, "type": "object", "required": ["authority_role", "reserve_limit", "payment_limit", "client_id"], "title": "ClientAuthorityThresholdCreate", "description": "Schema for creating client-specific authority thresholds."}