{"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the client/insurance company"}, "prefix": {"type": "string", "title": "Prefix", "description": "Unique 4-character prefix used in claim numbering"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Additional details about the client"}, "active": {"type": "boolean", "title": "Active", "description": "Whether the client is active in the system", "default": true}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the client"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "When the client was created"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "When the client was last updated"}}, "type": "object", "required": ["name", "prefix", "id", "created_at", "updated_at"], "title": "ClientResponse", "description": "Client response schema."}