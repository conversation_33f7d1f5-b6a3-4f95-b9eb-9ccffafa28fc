{"properties": {"client_id": {"type": "string", "format": "uuid", "title": "Client Id", "description": "ID of the client the FNOL belongs to"}, "reported_by": {"type": "string", "maxLength": 200, "title": "Reported By", "description": "Name of the person reporting the loss"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Detailed description of the incident"}, "incident_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Incident Date", "description": "Date when the incident occurred"}, "incident_location": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Incident Location", "description": "Physical location where the incident occurred"}, "policy_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Policy Number", "description": "Policy number associated with the FNOL"}, "reporter_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Reporter Phone", "description": "Reporter's phone number (US format, e.g., '************', '(*************', or '5551234567')"}, "reporter_email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Reporter Email", "description": "Reporter's email address (e.g., '<EMAIL>')"}, "incident_state": {"$ref": "./USState.json", "description": "US state where the incident occurred"}, "incident_time": {"anyOf": [{"type": "string", "format": "time"}, {"type": "null"}], "title": "Incident Time", "description": "Time when the incident occurred (e.g., '14:30:00' for 2:30 PM)"}, "reporter_relationship": {"anyOf": [{"$ref": "./ReporterRelationship.json"}, {"type": "null"}], "description": "Relationship of the reporter to the claim/incident"}, "communication_preference": {"anyOf": [{"$ref": "./CommunicationPreference.json"}, {"type": "null"}], "description": "Preferred method of communication for responses"}, "id": {"type": "string", "format": "uuid", "title": "Id", "description": "Unique identifier for the FNOL"}, "fnol_number": {"type": "string", "title": "Fnol Number", "description": "Unique FNOL reference number"}, "reported_at": {"type": "string", "format": "date-time", "title": "Reported At", "description": "Timestamp when the loss was reported"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At", "description": "When the FNOL was created in the system"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At", "description": "When the FNOL was last updated"}, "client": {"$ref": "./ClientResponse.json", "description": "Client details"}, "claims": {"items": {"$ref": "./ClaimResponseSchema.json"}, "type": "array", "title": "<PERSON><PERSON><PERSON>", "description": "Claims associated with this FNOL"}}, "type": "object", "required": ["client_id", "reported_by", "incident_state", "id", "fnol_number", "reported_at", "created_at", "updated_at", "client"], "title": "FNOLResponse", "description": "FNOL response schema.", "example": {"claims": [], "client": {"active": true, "created_at": "2024-01-01T00:00:00Z", "description": "Selective Insurance Company of America", "id": "123e4567-e89b-12d3-a456-426614174000", "name": "Selective Insurance", "prefix": "SLCT", "updated_at": "2024-01-01T00:00:00Z"}, "client_id": "123e4567-e89b-12d3-a456-426614174000", "communication_preference": "EMAIL", "created_at": "2024-01-01T00:00:00Z", "description": "Multi-vehicle accident on highway", "fnol_number": "SLCT-FNOL-0000001", "id": "123e4567-e89b-12d3-a456-426614174000", "incident_date": "2024-01-01", "incident_location": "I-<PERSON> <PERSON>, <PERSON> 123", "incident_state": "FL", "incident_time": "14:30:00", "policy_number": "POL123456789", "reported_at": "2024-01-01T00:00:00Z", "reported_by": "<PERSON>", "reporter_email": "<EMAIL>", "reporter_phone": "************", "reporter_relationship": "INSURED", "updated_at": "2024-01-01T00:00:00Z"}}