{"properties": {"name": {"type": "string", "title": "Name", "description": "Name of the client/insurance company"}, "prefix": {"type": "string", "title": "Prefix", "description": "Unique 4-character prefix used in claim numbering"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Additional details about the client"}, "active": {"type": "boolean", "title": "Active", "description": "Whether the client is active in the system", "default": true}}, "type": "object", "required": ["name", "prefix"], "title": "ClientCreate", "description": "Create client schema."}