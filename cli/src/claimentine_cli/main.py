"""Main CLI application."""

import typer

from claimentine_cli.commands.attorneys import app as attorneys_app
from claimentine_cli.commands.audit import app as audit_app
from claimentine_cli.commands.auth import app as auth_app
from claimentine_cli.commands.claims import app as claims_app
from claimentine_cli.commands.config import app as config_app
from claimentine_cli.commands.clients import app as clients_app
from claimentine_cli.commands.documents import documents_group
from claimentine_cli.commands.fnols import app as fnols_app
from claimentine_cli.commands.metrics import app as metrics_app
from claimentine_cli.commands.notes import notes_app
from claimentine_cli.commands.recovery import app as recovery_app
from claimentine_cli.commands.reports import app as reports_app
from claimentine_cli.commands.system import app as system_app
from claimentine_cli.commands.tasks import tasks_app
from claimentine_cli.commands.users import app as users_app
from claimentine_cli.commands.witnesses import app as witnesses_app
from claimentine_cli.config import Config

app = typer.Typer(help="Claimentine CLI")

# Register commands
app.add_typer(auth_app, name="auth")
app.add_typer(config_app, name="config")
app.add_typer(system_app, name="system")
app.add_typer(users_app, name="users")
app.add_typer(claims_app, name="claims")
app.add_typer(clients_app, name="clients")
app.add_typer(fnols_app, name="fnols")
app.add_typer(documents_group, name="documents")
app.add_typer(recovery_app, name="recovery")
app.add_typer(notes_app, name="notes")
app.add_typer(tasks_app, name="tasks")
app.add_typer(witnesses_app, name="witnesses")
app.add_typer(attorneys_app, name="attorneys")
app.add_typer(audit_app, name="audit")
app.add_typer(metrics_app, name="metrics")
app.add_typer(reports_app, name="reports")


def main() -> None:
    """Run the CLI application."""
    # Initialize config
    Config.init()
    app()
