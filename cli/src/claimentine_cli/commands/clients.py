"""Client CLI commands."""

import json
from typing import Optional
from uuid import <PERSON><PERSON>D

import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client
from claimentine_cli.constants import CLIENTS_PATH

app = typer.Typer(help="Client management commands")
console = Console()


@app.command()
def list(
    active_only: bool = typer.Option(False, help="Show only active clients"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """List all clients."""
    params = {}
    if active_only:
        params["active_only"] = "true"

    response = api_client.get(CLIENTS_PATH, params=params)
    clients = response.json()

    if not clients:
        if output == "json":
            print(json.dumps([]))
        else:
            console.print("No clients found")
        return

    if output == "json":
        print(json.dumps(clients, indent=2))
        return

    table = Table(title="Clients")
    table.add_column("ID", style="dim")
    table.add_column("Name", style="green")
    table.add_column("Prefix", style="blue")
    table.add_column("Description", style="yellow")
    table.add_column("Status", style="cyan")

    for client in clients:
        table.add_row(
            str(client["id"]),
            client["name"],
            client["prefix"],
            client.get("description", ""),
            "Active" if client["active"] else "Inactive",
        )

    console.print(table)


@app.command()
def get(
    client_id: UUID = typer.Argument(..., help="Client ID"),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """Get client details."""
    response = api_client.get(f"{CLIENTS_PATH}/{client_id}")
    client = response.json()

    if output == "json":
        console.print(json.dumps(client, indent=2))
        return

    table = Table(title=f"Client Details - {client['name']}", show_header=False)
    table.add_column("Field", style="cyan")
    table.add_column("Value", style="green")

    table.add_row("ID", str(client["id"]))
    table.add_row("Name", client["name"])
    table.add_row("Prefix", client["prefix"])
    table.add_row("Description", client.get("description", ""))
    table.add_row("Status", "Active" if client["active"] else "Inactive")
    table.add_row("Created At", client["created_at"])
    table.add_row("Updated At", client["updated_at"])

    console.print(table)


@app.command()
def create(
    name: str = typer.Option(..., help="Client name"),
    prefix: str = typer.Option(..., help="Client prefix (4 uppercase alphanumeric characters)"),
    description: Optional[str] = typer.Option(None, help="Client description"),
    output: str = typer.Option("text", help="Output format: text or json"),
) -> None:
    """Create a new client."""
    data = {
        "name": name,
        "prefix": prefix,
        "description": description,
    }

    response = api_client.post(CLIENTS_PATH, json=data)
    client = response.json()

    if output == "json":
        print(json.dumps(client, indent=2))
        return

    console.print("Client created successfully!", style="green")
    console.print(f"Name: {client['name']}")
    console.print(f"Prefix: {client['prefix']}")
    console.print(f"ID: {client['id']}")


@app.command()
def update(
    client_id: UUID = typer.Argument(..., help="Client ID"),
    name: Optional[str] = typer.Option(None, help="Updated client name"),
    description: Optional[str] = typer.Option(None, help="Updated client description"),
    active: Optional[bool] = typer.Option(
        None, "--active/--no-active", help="Update active status"
    ),
    output: str = typer.Option("table", help="Output format: table or json"),
) -> None:
    """Update a client's details."""
    # Prepare update data
    data = {}
    if name is not None:
        data["name"] = name
    if description is not None:
        data["description"] = description
    if active is not None:
        data["active"] = active

    if not data:
        console.print("No update fields provided", style="yellow")
        raise typer.Exit(1)

    try:
        # Call the API to update the client
        response = api_client.patch(f"{CLIENTS_PATH}/{client_id}", json=data)
        client = response.json()

        if output == "json":
            console.print(json.dumps(client, indent=2))
            return

        console.print("Client updated successfully!", style="green")

        # Display the updated client details
        table = Table(title=f"Updated Client - {client['name']}", show_header=False)
        table.add_column("Field", style="cyan")
        table.add_column("Value", style="green")

        table.add_row("ID", str(client["id"]))
        table.add_row("Name", client["name"])
        table.add_row("Prefix", client["prefix"])
        table.add_row("Description", client.get("description", ""))
        table.add_row("Status", "Active" if client["active"] else "Inactive")
        table.add_row("Created At", client["created_at"])
        table.add_row("Updated At", client["updated_at"])

        console.print(table)

    except Exception as e:
        console.print(f"Error updating client: {e}", style="red")
        raise typer.Exit(1)


@app.command("delete")
def delete_client(
    client_id: UUID = typer.Argument(..., help="ID of the client to delete"),
    output: str = typer.Option("text", help="Output format: text or json"),
) -> None:
    """Delete a client (soft delete)."""
    try:
        api_client.delete(f"{CLIENTS_PATH}/{client_id}")
        # delete returns 204 No Content, so response.json() will fail
        # We just check the status code was success (2xx)

        if output == "json":
            # Indicate success with a simple JSON message
            print(json.dumps({"message": "Client deleted successfully", "id": str(client_id)}))
        else:
            console.print(f"Client {client_id} deleted successfully.", style="green")

    except Exception as e:
        # Handle potential errors (e.g., 404 Not Found, 403 Forbidden)
        if output == "json":
            print(json.dumps({"error": str(e), "id": str(client_id)}))
        else:
            console.print(f"Error deleting client {client_id}: {e}", style="red")
        raise typer.Exit(1)


if __name__ == "__main__":
    app()
