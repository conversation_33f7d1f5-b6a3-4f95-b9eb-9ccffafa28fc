"""CLI commands for client-specific authority thresholds."""

import json
import logging
from decimal import Decimal
from typing import List, Optional
from uuid import UUID

import click
import typer
from rich.console import Console
from rich.table import Table

from claimentine_cli.api import api_client

logger = logging.getLogger(__name__)
console = Console()

app = typer.Typer(
    name="authority",
    help="Manage client-specific authority thresholds",
    no_args_is_help=True,
)


@app.command()
def list(
    client_id: UUID = typer.Option(..., "--client-id", "-c", help="ID of the client"),
    all_clients: bool = typer.Option(
        False, "--all", "-a", help="List thresholds for all clients", is_flag=True
    ),
    json_output: bool = typer.Option(
        False, "--json", "-j", help="Output in JSON format", is_flag=True
    ),
):
    """List client-specific authority thresholds."""
    if not all_clients and not client_id:
        typer.echo("Either client-id or --all must be specified")
        raise typer.Exit(code=1)

    try:
        if all_clients:
            # Get list of customers first
            clients_response = api_client.get("/clients")
            clients = clients_response.json()

            all_thresholds = []
            for client in clients:
                client_id = client["id"]
                client_name = client["name"]
                typer.echo(f"Fetching thresholds for client {client_name} ({client_id})...")

                response = api_client.get(f"/config/clients/{client_id}/authority")
                thresholds = response.json()

                for threshold in thresholds:
                    threshold["client_name"] = client_name

                all_thresholds.extend(thresholds)

            thresholds = all_thresholds
        else:
            response = api_client.get(f"/config/clients/{client_id}/authority")
            thresholds = response.json()

            # Get client name
            client_response = api_client.get(f"/clients/{client_id}")
            client = client_response.json()
            client_name = client.get("name", "Unknown")

            for threshold in thresholds:
                threshold["client_name"] = client_name

        if json_output:
            typer.echo(json.dumps(thresholds, indent=2, default=str))
        else:
            if not thresholds:
                typer.echo("No authority thresholds found.")
                return

            # Use Rich table instead of tabulate
            table = Table(title="Client Authority Thresholds")
            table.add_column("Client", style="green")
            table.add_column("Authority Role", style="blue")
            table.add_column("Reserve Limit", style="cyan")
            table.add_column("Payment Limit", style="cyan")
            table.add_column("Description", style="yellow")

            for threshold in thresholds:
                table.add_row(
                    threshold.get("client_name", "Unknown"),
                    threshold["authority_role"],
                    str(threshold["reserve_limit"]),
                    str(threshold["payment_limit"]),
                    threshold.get("description", ""),
                )

            console.print(table)

    except Exception as e:
        typer.echo(f"Error: {str(e)}")
        logger.exception("Error listing client authority thresholds")
        raise typer.Exit(code=1)


@app.command()
def create(
    client_id: UUID = typer.Option(..., "--client-id", "-c", help="ID of the client"),
    authority_role: str = typer.Option(
        ..., "--role", "-r", help="Authority role (e.g., BASIC, SENIOR)"
    ),
    reserve_limit: float = typer.Option(..., "--reserve-limit", help="Reserve limit amount"),
    payment_limit: float = typer.Option(..., "--payment-limit", help="Payment limit amount"),
    description: str = typer.Option("", "--description", "-d", help="Optional description"),
):
    """Create a new client-specific authority threshold."""
    try:
        # Get client name for better feedback
        client_response = api_client.get(f"/clients/{client_id}")
        client = client_response.json()
        client_name = client.get("name", "Unknown")

        # Create payload
        payload = {
            "client_id": str(client_id),
            "authority_role": authority_role,
            "reserve_limit": str(reserve_limit),
            "payment_limit": str(payment_limit),
            "description": description,
        }

        response = api_client.post(f"/config/clients/{client_id}/authority", json=payload)
        threshold = response.json()

        typer.echo(f"Successfully created authority threshold for client {client_name}:")
        typer.echo(f"  Authority Role: {threshold['authority_role']}")
        typer.echo(f"  Reserve Limit:  {threshold['reserve_limit']}")
        typer.echo(f"  Payment Limit:  {threshold['payment_limit']}")
        typer.echo(f"  Description:    {threshold.get('description', '')}")

    except Exception as e:
        typer.echo(f"Error: {str(e)}")
        logger.exception("Error creating client authority threshold")
        raise typer.Exit(code=1)


@app.command()
def update(
    threshold_id: UUID = typer.Option(..., "--id", help="ID of the threshold to update"),
    reserve_limit: Optional[float] = typer.Option(
        None, "--reserve-limit", help="New reserve limit amount"
    ),
    payment_limit: Optional[float] = typer.Option(
        None, "--payment-limit", help="New payment limit amount"
    ),
    description: Optional[str] = typer.Option(None, "--description", "-d", help="New description"),
):
    """Update an existing client-specific authority threshold."""
    try:
        # Get current threshold for better feedback
        current_response = api_client.get(f"/config/authority/{threshold_id}")
        current = current_response.json()

        # Get client name
        client_id = current.get("client_id")
        client_response = api_client.get(f"/clients/{client_id}")
        client = client_response.json()
        client_name = client.get("name", "Unknown")

        # Create payload with only fields that are being updated
        payload = {}
        if reserve_limit is not None:
            payload["reserve_limit"] = str(reserve_limit)
        if payment_limit is not None:
            payload["payment_limit"] = str(payment_limit)
        if description is not None:
            payload["description"] = description

        if not payload:
            typer.echo(
                "No updates specified. Use --reserve-limit, --payment-limit, or --description."
            )
            raise typer.Exit(code=1)

        response = api_client.patch(f"/config/authority/{threshold_id}", json=payload)
        updated = response.json()

        typer.echo(f"Successfully updated authority threshold for client {client_name}:")
        typer.echo(f"  Authority Role: {updated['authority_role']}")
        typer.echo(
            f"  Reserve Limit:  {updated['reserve_limit']} (was: {current['reserve_limit']})"
        )
        typer.echo(
            f"  Payment Limit:  {updated['payment_limit']} (was: {current['payment_limit']})"
        )
        typer.echo(f"  Description:    {updated.get('description', '')}")

    except Exception as e:
        typer.echo(f"Error: {str(e)}")
        logger.exception("Error updating client authority threshold")
        raise typer.Exit(code=1)


@app.command()
def delete(
    threshold_id: UUID = typer.Option(..., "--id", help="ID of the threshold to delete"),
    force: bool = typer.Option(
        False, "--force", "-f", help="Skip confirmation prompt", is_flag=True
    ),
):
    """Delete a client-specific authority threshold."""
    try:
        # Get current threshold for better feedback
        current_response = api_client.get(f"/config/authority/{threshold_id}")
        current = current_response.json()

        # Get client name
        client_id = current.get("client_id")
        client_response = api_client.get(f"/clients/{client_id}")
        client = client_response.json()
        client_name = client.get("name", "Unknown")

        if not force:
            typer.echo(
                f"About to delete authority threshold for client {client_name} "
                f"with role {current['authority_role']} and reserve limit {current['reserve_limit']}"
            )
            if not click.confirm("Are you sure?"):
                typer.echo("Deletion canceled.")
                return

        api_client.delete(f"/config/authority/{threshold_id}")
        typer.echo(f"Successfully deleted authority threshold for client {client_name}")

    except Exception as e:
        typer.echo(f"Error: {str(e)}")
        logger.exception("Error deleting client authority threshold")
        raise typer.Exit(code=1)
