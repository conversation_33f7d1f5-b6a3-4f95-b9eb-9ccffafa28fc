"""Base service for reports."""

import logging
from datetime import datetime, timedelta
from typing import Dict, <PERSON>, Optional, Tuple, Union
from uuid import UUID

from sqlalchemy.orm import Session

from claimentine.core.exceptions import BadRequestError
from claimentine.models.user import User
from claimentine.schemas.metrics.dashboard import Met<PERSON><PERSON><PERSON>e
from claimentine.schemas.reports.base import ReportMetadata, ReportRequest
from claimentine.services.base import BaseService

logger = logging.getLogger(__name__)


class BaseReportService(BaseService):
    """Base service for reports."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def _calculate_date_range(self, request: ReportRequest) -> Tuple[datetime, datetime]:
        """Calculate date range based on request parameters.

        Args:
            request: Report request with date parameters

        Returns:
            Tuple of (start_date, end_date)
        """
        end_date = datetime.utcnow()
        start_date = None

        # If explicit dates are provided, use them
        if request.start_date and request.end_date:
            return request.start_date, request.end_date

        # If only end_date is provided, we need to determine start_date based on period
        if request.end_date:
            end_date = request.end_date

        # If only start_date is provided, use it with current time as end_date
        if request.start_date:
            return request.start_date, end_date

        # If period is provided, calculate date range
        if request.period:
            if request.period == "last_30_days":
                start_date = end_date - timedelta(days=30)
            elif request.period == "last_6_months":
                start_date = end_date - timedelta(days=180)
            elif request.period == "last_1_year":
                start_date = end_date - timedelta(days=365)
            else:
                raise BadRequestError(f"Invalid period: {request.period}")
            return start_date, end_date

        # If no date parameters are provided, return None for start_date to indicate no filtering
        # This fixes the issue where default 30-day filtering was applied incorrectly
        return None, end_date

    def _calculate_metric_change(
        self, current_value: Union[int, float], previous_value: Union[int, float]
    ) -> Optional[MetricChange]:
        """Calculate metric change between current and previous values.

        Args:
            current_value: Current period value
            previous_value: Previous period value

        Returns:
            MetricChange object or None if previous_value is zero or values are equal
        """
        if previous_value == 0 or current_value == previous_value:
            return MetricChange(value=0, direction="neutral", percentage=0.0)

        change_value = current_value - previous_value
        change_percentage = (change_value / previous_value) * 100.0

        direction = "increase" if change_value > 0 else "decrease"

        return MetricChange(value=abs(change_value), direction=direction, percentage=abs(change_percentage))

    def _create_report_metadata(
        self, report_name: str, request: ReportRequest, column_headers: List[Dict[str, str]]
    ) -> ReportMetadata:
        """Create standardized report metadata.

        Args:
            report_name: Name of the report
            request: Report request with filters
            column_headers: Column headers for the report

        Returns:
            ReportMetadata object
        """
        start_date, end_date = self._calculate_date_range(request)

        return ReportMetadata(
            report_name=report_name,
            generated_at=datetime.utcnow(),
            filters_applied={
                "period": request.period,
                "start_date": start_date.isoformat() if start_date else None,
                "end_date": end_date.isoformat() if end_date else None,
                "client_id": request.client_id,
                "claim_type": request.claim_type,
                "user_id": request.user_id,
            },
            column_headers=column_headers,
        )
