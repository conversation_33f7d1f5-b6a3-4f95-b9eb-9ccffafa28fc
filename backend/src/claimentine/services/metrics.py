"""Service for metrics and analytics."""

import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Tuple, Union
from uuid import UUID

from sqlalchemy import and_, case, func, or_, select
from sqlalchemy.orm import Session

from claimentine.core.exceptions import BadRequestError
from claimentine.models.claim.base import BaseClaim, ClaimStatus
from claimentine.models.claim.financial import ClaimFinancials, ClaimReserve, ReserveHistory
from claimentine.models.fnol import FNOL
from claimentine.models.task import Task, TaskStatus
from claimentine.models.user import User
from claimentine.schemas.metrics.dashboard import DashboardMetricsResponse, MetricChange
from claimentine.services.base import BaseService

logger = logging.getLogger(__name__)


class MetricsService(BaseService):
    """Service for metrics and analytics."""

    def __init__(self, db: Session, current_user: Optional[User] = None):
        """Initialize service."""
        super().__init__(db, current_user)

    def _calculate_date_range(self, period: str) -> Tuple[datetime, datetime]:
        """Calculate date range based on period.

        Args:
            period: Period string (e.g., 'last_30_days', 'last_6_months', etc.)

        Returns:
            Tuple of (start_date, end_date)

        Raises:
            BadRequestError: If period is not valid
        """
        end_date = datetime.utcnow()

        if period == "last_7_days":
            start_date = end_date - timedelta(days=7)
        elif period == "last_30_days":
            start_date = end_date - timedelta(days=30)
        elif period == "last_90_days":
            start_date = end_date - timedelta(days=90)
        elif period == "last_6_months":
            start_date = end_date - timedelta(days=180)  # Approximate 6 months as 180 days
        elif period == "thisMonth":
            start_date = datetime(end_date.year, end_date.month, 1)
        elif period == "lastMonth":
            # Last month's first day
            if end_date.month == 1:
                start_date = datetime(end_date.year - 1, 12, 1)
            else:
                start_date = datetime(end_date.year, end_date.month - 1, 1)
            # Last month's last day
            if end_date.month == 1:
                end_date = datetime(end_date.year, 1, 1) - timedelta(days=1)
            else:
                end_date = datetime(end_date.year, end_date.month, end_date.day, 23, 59, 59)
        elif period == "thisQuarter":
            current_quarter = ((end_date.month - 1) // 3) + 1
            start_date = datetime(end_date.year, (current_quarter - 1) * 3 + 1, 1)
        elif period == "thisYear":
            start_date = datetime(end_date.year, 1, 1)
        else:
            raise BadRequestError(f"Invalid period: {period}")

        return start_date, end_date

    def _calculate_previous_period(self, start_date: datetime, end_date: datetime) -> Tuple[datetime, datetime]:
        """Calculate the previous period of equal length.

        Args:
            start_date: Start date of current period
            end_date: End date of current period

        Returns:
            Tuple of (previous_start_date, previous_end_date)
        """
        period_length = end_date - start_date
        previous_end_date = start_date - timedelta(microseconds=1)
        previous_start_date = previous_end_date - period_length

        return previous_start_date, previous_end_date

    def _calculate_metric_change(
        self, current_value: Union[int, float, Decimal], previous_value: Union[int, float, Decimal]
    ) -> Optional[MetricChange]:
        """Calculate metric change between current and previous values.

        Args:
            current_value: Current period value
            previous_value: Previous period value

        Returns:
            MetricChange object or None if previous_value is zero or values are equal
        """
        if previous_value == 0 or current_value == previous_value:
            return MetricChange(value=0, direction="neutral", percentage=0.0)

        change_value = current_value - previous_value
        change_percentage = (change_value / previous_value) * 100.0

        direction = "increase" if change_value > 0 else "decrease"

        return MetricChange(value=abs(change_value), direction=direction, percentage=abs(change_percentage))

    def get_dashboard_metrics(
        self,
        period: str = "last_30_days",
        compare_period: bool = True,
        client_id: Optional[UUID] = None,
        user_id: Optional[UUID] = None,
    ) -> DashboardMetricsResponse:
        """Get dashboard metrics.

        Args:
            period: Time period (e.g., 'last_30_days', 'last_6_months', etc.)
            compare_period: Whether to compare with previous period
            client_id: Filter by client ID
            user_id: Filter by user ID

        Returns:
            DashboardMetricsResponse object
        """
        # Check permission to view metrics
        self.check_permission("VIEW_METRICS", "metrics")

        # Calculate date ranges
        current_start, current_end = self._calculate_date_range(period)
        if compare_period:
            previous_start, previous_end = self._calculate_previous_period(current_start, current_end)

        # Initialize values that might be used only with compare_period=True
        reserve_history_result = Decimal(0)
        total_reserves_change = None
        total_payments_change = None
        total_claims_change = None
        avg_lifecycle_change = None

        # Base claim query filters
        claim_filters = [BaseClaim.is_deleted == False]
        if client_id:
            claim_filters.append(BaseClaim.client_id == client_id)
        if user_id:
            claim_filters.append(BaseClaim.assigned_to_id == user_id)

        # Define closed statuses condition
        is_closed_condition = BaseClaim.status == ClaimStatus.CLOSURE

        # --- Calculate total claims --- #
        total_claims_stmt = select(func.count()).select_from(BaseClaim).where(*claim_filters)
        total_claims = self.db.scalar(total_claims_stmt) or 0

        # --- Calculate open claims --- #
        open_claims_stmt = select(func.count()).select_from(BaseClaim).where(*claim_filters, ~is_closed_condition)
        open_claims = self.db.scalar(open_claims_stmt) or 0

        # --- Calculate new claims in period --- #
        new_claims_stmt = (
            select(func.count())
            .select_from(BaseClaim)
            .where(*claim_filters, BaseClaim.created_at >= current_start, BaseClaim.created_at <= current_end)
        )
        new_claims = self.db.scalar(new_claims_stmt) or 0

        # --- Calculate closed claims in period --- #
        closed_claims_stmt = (
            select(func.count())
            .select_from(BaseClaim)
            .where(
                *claim_filters,
                is_closed_condition,
                BaseClaim.closed_at >= current_start,
                BaseClaim.closed_at <= current_end,
            )
        )
        closed_claims = self.db.scalar(closed_claims_stmt) or 0

        # --- Calculate average claim lifecycle --- #
        avg_lifecycle_stmt = (
            select(func.avg(BaseClaim.closed_at - BaseClaim.created_at))
            .select_from(BaseClaim)
            .where(
                *claim_filters,
                is_closed_condition,
                BaseClaim.closed_at >= current_start,
                BaseClaim.closed_at <= current_end,
            )
        )
        avg_lifecycle_result = self.db.scalar(avg_lifecycle_stmt)
        avg_lifecycle_days = avg_lifecycle_result.total_seconds() / (60 * 60 * 24) if avg_lifecycle_result else None

        # --- Calculate total payments in period --- #
        payments_stmt = (
            select(
                func.sum(ClaimFinancials.indemnity_paid)
                + func.sum(ClaimFinancials.expense_paid)
                + func.sum(ClaimFinancials.defense_paid)
            )
            .select_from(ClaimFinancials)
            .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
            .where(
                *claim_filters,
                ClaimFinancials.last_payment_date >= current_start,
                ClaimFinancials.last_payment_date <= current_end,
            )
        )
        total_payments = self.db.scalar(payments_stmt) or Decimal(0)

        # --- Calculate total outstanding reserves --- #
        reserves_stmt = (
            select(func.sum(ClaimReserve.amount))
            .select_from(ClaimReserve)
            .join(ClaimFinancials, ClaimReserve.financials_id == ClaimFinancials.id)
            .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
            .where(*claim_filters, ~is_closed_condition)  # Only include open claims
        )
        total_reserves = self.db.scalar(reserves_stmt) or Decimal(0)

        # --- Calculate pending tasks --- #
        task_filters = []  # Task doesn't have is_deleted field
        if client_id:
            task_filters.append(Task.claim_id.in_(select(BaseClaim.id).where(BaseClaim.client_id == client_id)))
        if user_id:
            task_filters.append(Task.assigned_to_id == user_id)

        pending_tasks_stmt = (
            select(func.count()).select_from(Task).where(*task_filters, Task.status == TaskStatus.PENDING)
        )
        pending_tasks = self.db.scalar(pending_tasks_stmt) or 0

        # --- Calculate overdue tasks --- #
        overdue_tasks_stmt = (
            select(func.count())
            .select_from(Task)
            .where(*task_filters, Task.due_date < datetime.utcnow(), Task.status == TaskStatus.PENDING)
        )
        overdue_tasks = self.db.scalar(overdue_tasks_stmt) or 0

        # --- Calculate pending FNOLs --- #
        fnol_filters = [FNOL.is_deleted == False]
        if client_id:
            fnol_filters.append(FNOL.client_id == client_id)

        pending_fnols_stmt = (
            select(func.count())
            .select_from(FNOL)
            .where(*fnol_filters, ~FNOL.claims.any())  # FNOLs not yet assigned to claims
        )
        pending_fnols = self.db.scalar(pending_fnols_stmt) or 0

        # --- Calculate changes compared to previous period --- #
        avg_lifecycle_days_change = None
        total_payments_change = None
        total_reserves_change = None

        if compare_period:
            # Previous period average lifecycle
            prev_avg_lifecycle_stmt = (
                select(func.avg(BaseClaim.closed_at - BaseClaim.created_at))
                .select_from(BaseClaim)
                .where(
                    *claim_filters,
                    is_closed_condition,
                    BaseClaim.closed_at >= previous_start,
                    BaseClaim.closed_at <= previous_end,
                )
            )
            prev_lifecycle_result = self.db.scalar(prev_avg_lifecycle_stmt)
            prev_avg_lifecycle_days = (
                prev_lifecycle_result.total_seconds() / (60 * 60 * 24) if prev_lifecycle_result else None
            )

            if prev_avg_lifecycle_days and avg_lifecycle_days:
                avg_lifecycle_days_change = self._calculate_metric_change(avg_lifecycle_days, prev_avg_lifecycle_days)

            # Previous period payments
            prev_payments_stmt = (
                select(
                    func.sum(ClaimFinancials.indemnity_paid)
                    + func.sum(ClaimFinancials.expense_paid)
                    + func.sum(ClaimFinancials.defense_paid)
                )
                .select_from(ClaimFinancials)
                .join(BaseClaim, ClaimFinancials.claim_id == BaseClaim.id)
                .where(
                    *claim_filters,
                    ClaimFinancials.last_payment_date >= previous_start,
                    ClaimFinancials.last_payment_date <= previous_end,
                )
            )
            prev_total_payments = self.db.scalar(prev_payments_stmt) or Decimal(0)
            total_payments_change = self._calculate_metric_change(total_payments, prev_total_payments)

            # Previous period reserves
            # Get the reserves as of the previous period end date
            prev_reserves_stmt = (
                select(func.sum(ReserveHistory.new_amount))
                .select_from(ReserveHistory)
                .join(BaseClaim, ReserveHistory.claim_id == BaseClaim.id)
                .where(*claim_filters, ReserveHistory.changed_at <= previous_end)
                .group_by(ReserveHistory.claim_id)
                .having(func.max(ReserveHistory.changed_at) == ReserveHistory.changed_at)
            )
            # This won't work because changed_at is referenced directly but not in GROUP BY
            # Let's change our approach to find the latest reserve history per claim

            # Create a subquery to find the most recent reserve entry for each claim
            latest_reserve_subquery = (
                select(ReserveHistory.claim_id, func.max(ReserveHistory.changed_at).label("max_changed_at"))
                .select_from(ReserveHistory)
                .where(ReserveHistory.changed_at <= previous_end)
                .group_by(ReserveHistory.claim_id)
                .subquery()
            )

            # Join with the subquery to get the actual reserve amounts
            prev_reserves_stmt = (
                select(func.sum(ReserveHistory.new_amount))
                .select_from(ReserveHistory)
                .join(
                    latest_reserve_subquery,
                    and_(
                        ReserveHistory.claim_id == latest_reserve_subquery.c.claim_id,
                        ReserveHistory.changed_at == latest_reserve_subquery.c.max_changed_at,
                    ),
                )
                .join(BaseClaim, ReserveHistory.claim_id == BaseClaim.id)
                .where(*claim_filters)
            )

            prev_total_reserves = self.db.scalar(prev_reserves_stmt) or Decimal(0)
            total_reserves_change = self._calculate_metric_change(total_reserves, prev_total_reserves)

        # --- Calculate reserve trend --- #
        # We want to know how reserves have changed over time
        # We'll use the reserve history table to get this data
        if compare_period:
            reserve_history_filters = []
            if client_id:
                reserve_history_filters.append(
                    ReserveHistory.claim_id.in_(select(BaseClaim.id).where(BaseClaim.client_id == client_id))
                )

            # Get reserve totals at the end of the comparison period
            # Create a subquery for latest reserves at the previous end date
            latest_reserve_trend_subquery = (
                select(ReserveHistory.claim_id, func.max(ReserveHistory.changed_at).label("max_changed_at"))
                .select_from(ReserveHistory)
                .where(ReserveHistory.changed_at <= previous_end)
                .group_by(ReserveHistory.claim_id)
                .subquery()
            )

            # Main query to sum up the reserve amounts
            reserve_history_stmt = (
                select(func.sum(ReserveHistory.new_amount))
                .select_from(ReserveHistory)
                .join(
                    latest_reserve_trend_subquery,
                    and_(
                        ReserveHistory.claim_id == latest_reserve_trend_subquery.c.claim_id,
                        ReserveHistory.changed_at == latest_reserve_trend_subquery.c.max_changed_at,
                    ),
                )
                .where(*reserve_history_filters)
            )

            reserve_history_result = self.db.scalar(reserve_history_stmt) or Decimal(0)

        # Construct and return the response
        return DashboardMetricsResponse(
            total_claims=total_claims,
            open_claims=open_claims,
            new_claims_last_period=new_claims,
            closed_claims_last_period=closed_claims,
            average_claim_lifecycle_days=avg_lifecycle_days,
            average_claim_lifecycle_days_change=avg_lifecycle_days_change,
            total_payments_last_period=total_payments,
            total_payments_change=total_payments_change,
            total_outstanding_reserves=total_reserves,
            total_outstanding_reserves_change=total_reserves_change,
            tasks_pending=pending_tasks,
            tasks_overdue=overdue_tasks,
            fnols_pending=pending_fnols,
            reserve_trend=reserve_history_result,
        )
