"""FNOL service."""

from datetime import datetime
from typing import List, Optional, Set
from uuid import UUID

from sqlalchemy import select
from sqlalchemy.orm import Session, joinedload

from claimentine.core.exceptions import DuplicateError, NotFoundError, ValidationError
from claimentine.models.fnol import FNOL
from claimentine.models.user import User
from claimentine.schemas.fnol import FNOLCreate, FNOLUpdate
from claimentine.services.base import BaseService
from claimentine.services.client import ClientService


class FNOLService(BaseService):
    """Service for managing First Notice of Loss (FNOL) records."""

    def __init__(
        self, db: Session, current_user: Optional[User] = None, client_service: Optional[ClientService] = None
    ):
        """Initialize service with dependencies."""
        super().__init__(db, current_user)
        # Store injected service, or create default if not provided (less ideal)
        self.client_service = client_service or ClientService(db, current_user)

    def _apply_includes(self, query: select, includes: Optional[Set[str]] = None) -> select:
        """Apply relationship includes to query."""
        if not includes:
            return query

        if "customer" in includes:
            query = query.options(joinedload(FNOL.customer))
        if "claims" in includes:
            query = query.options(joinedload(FNOL.claims))

        return query

    def _generate_fnol_number(self, customer_id: UUID) -> str:
        """Generate a sequential FNOL number in format PREFIX-FNOL-NNNNNNN."""
        # Get customer prefix using injected service
        customer = self.customer_service.get_customer_by_id(customer_id)
        if not customer:
            raise NotFoundError(f"Customer {customer_id} not found")

        # Get the highest FNOL number for the customer
        query = (
            select(FNOL.fnol_number)
            .where(FNOL.fnol_number.like(f"{customer.prefix}-FNOL-%"))
            .order_by(FNOL.fnol_number.desc())
            .limit(1)
        )
        last_fnol = self.db.scalar(query)

        if last_fnol:
            # Extract the sequence number and increment
            try:
                sequence = int(last_fnol.split("-")[-1]) + 1
            except (ValueError, IndexError):
                # If parsing fails, start from 1
                sequence = 1
        else:
            # First FNOL for this customer
            sequence = 1

        return f"{customer.prefix}-FNOL-{sequence:07d}"

    def list_fnols(
        self,
        skip: int = 0,
        limit: int = 10,
        customer_id: Optional[UUID] = None,
        includes: Optional[Set[str]] = None,
    ) -> List[FNOL]:
        """List FNOLs with filtering based on user permissions."""
        # Check permission to view FNOLs
        self.check_permission("VIEW_ALL_CLAIMS", "fnol")

        stmt = select(FNOL)

        # Apply filters
        if customer_id:
            stmt = stmt.where(FNOL.customer_id == customer_id)

        # Apply includes
        stmt = self._apply_includes(stmt, includes)

        # Filter out soft-deleted records
        stmt = stmt.where(FNOL.is_deleted == False)

        # Apply pagination
        stmt = stmt.offset(skip).limit(limit)

        return list(self.db.scalars(stmt))

    def get_fnol_by_id(self, fnol_id: UUID, includes: Optional[Set[str]] = None) -> Optional[FNOL]:
        """Get FNOL by ID."""
        stmt = select(FNOL).where(FNOL.id == fnol_id)
        stmt = self._apply_includes(stmt, includes)
        fnol = self.db.scalar(stmt)

        if not fnol:
            return None
        # Ensure soft-deleted are not returned
        if fnol.is_deleted:
            return None

        # Check permission to view this FNOL
        self.check_permission("VIEW_ALL_CLAIMS", "fnol", fnol_id)

        return fnol

    def get_fnol_by_number(self, fnol_number: str, includes: Optional[Set[str]] = None) -> Optional[FNOL]:
        """Get FNOL by number."""
        stmt = select(FNOL).where(FNOL.fnol_number == fnol_number)
        stmt = self._apply_includes(stmt, includes)
        fnol = self.db.scalar(stmt)

        if not fnol:
            return None
        # Ensure soft-deleted are not returned
        if fnol.is_deleted:
            return None

        # Check permission to view this FNOL
        self.check_permission("VIEW_ALL_CLAIMS", "fnol", fnol_number)

        return fnol

    def create_fnol(self, data: FNOLCreate) -> FNOL:
        """Create a new FNOL."""
        # Check permission to create FNOLs
        self.check_permission("CREATE_CLAIMS", resource_type="fnol")

        # Validate client exists and is active using injected service
        # Ensure the injected service has the correct context (db, user)
        # If client_service wasn't injected, __init__ created one with the correct context.
        client = self.client_service.get_client_by_id(data.client_id)
        if not client:
            raise NotFoundError(f"Client {data.client_id} not found")
        if not client.active:
            raise ValidationError(f"Client {client.name} is not active")

        # Generate FNOL number if not provided
        if not data.fnol_number:
            data.fnol_number = self._generate_fnol_number(data.customer_id)
        # Check if FNOL number already exists (and is not soft-deleted)
        elif self.get_fnol_by_number(data.fnol_number):
            raise DuplicateError(f"FNOL number {data.fnol_number} already exists")

        # Create FNOL
        fnol = FNOL(**data.model_dump(exclude_unset=True))
        self.db.add(fnol)
        self.db.commit()
        self.db.refresh(fnol)

        return fnol

    def update_fnol(self, fnol_id: UUID, data: FNOLUpdate) -> FNOL:
        """Update an existing FNOL."""
        # Check permission to update FNOLs
        self.check_permission("EDIT_CLAIMS", "fnol", fnol_id)

        # Get FNOL
        fnol = self.get_fnol_by_id(fnol_id)
        if not fnol:
            raise NotFoundError(f"FNOL {fnol_id} not found")

        # Update fields
        update_data = data.model_dump(exclude_unset=True)
        for key, value in update_data.items():
            setattr(fnol, key, value)

        self.db.commit()
        self.db.refresh(fnol)
        return fnol

    def delete_fnol(self, fnol_id: UUID) -> None:
        """Delete an FNOL.

        Note: This will fail if there are any claims associated with the FNOL
        due to the foreign key constraint.
        """
        # Check permission to delete FNOLs
        self.check_permission("DELETE_CLAIMS", "fnol", fnol_id)

        fnol = self.get_fnol_by_id(fnol_id)
        if not fnol:
            raise NotFoundError(f"FNOL {fnol_id} not found")

        # Soft delete: Set flags
        fnol.is_deleted = True
        fnol.deleted_at = datetime.utcnow()
        # self.db.delete(fnol) # Removed hard delete
        self.db.commit()
