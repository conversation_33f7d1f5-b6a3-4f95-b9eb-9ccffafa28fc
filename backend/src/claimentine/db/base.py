"""Import all models here for SQLAlchemy to discover them."""

from claimentine.db.base_class import Base  # noqa: F401
from claimentine.db.session import engine, get_db  # noqa: F401
from claimentine.models.associations import UserPermission  # noqa: F401
from claimentine.models.attorney import Attorney  # noqa: F401
from claimentine.models.audit import AuditTrail  # noqa: F401
from claimentine.models.auth import Permission, UserSession  # noqa: F401
from claimentine.models.authority import AuthorityLevel  # noqa: F401
from claimentine.models.claim.base import BaseClaim  # noqa: F401
from claimentine.models.claim.details import AutoDetails, PropertyDetails  # noqa: F401
from claimentine.models.claim.financial import ClaimFinancials  # noqa: F401
from claimentine.models.claim.types import AutoClaim, PropertyClaim  # noqa: F401
from claimentine.models.config.system import SystemConfiguration  # noqa: F401
from claimentine.models.counter import ClientTaskCounter  # noqa: F401
from claimentine.models.client import Client  # noqa: F401
from claimentine.models.document import Document  # noqa: F401
from claimentine.models.fnol import FNOL  # noqa: F401
from claimentine.models.note import Note  # noqa: F401
from claimentine.models.status_history import ClaimStatusHistory  # noqa: F401
from claimentine.models.task import Task  # noqa: F401
from claimentine.models.user import User  # noqa: F401
from claimentine.models.witness import Witness  # noqa: F401

# List all models here for easy access
__all__ = [
    "Base",
    "engine",
    "get_db",
    "UserPermission",
    "Permission",
    "UserSession",
    "AuthorityLevel",
    "BaseClaim",
    "AutoDetails",
    "PropertyDetails",
    "ClaimFinancials",
    "AutoClaim",
    "PropertyClaim",
    "Customer",
    "Document",
    "FNOL",
    "Note",
    "ClaimStatusHistory",
    "Task",
    "User",
    "CustomerTaskCounter",
    "Attorney",
    "Witness",
    "AuditTrail",
    "SystemConfiguration",
]
