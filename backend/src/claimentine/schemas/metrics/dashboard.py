"""Pydantic schemas for dashboard metrics."""

from decimal import Decimal
from typing import Literal, Optional

from pydantic import BaseModel, Field


class MetricChange(BaseModel):
    """Schema for representing metric changes with direction and percentage."""

    value: float | int | Decimal = Field(..., description="Absolute value of the change")
    direction: Literal["increase", "decrease", "neutral"] = Field(..., description="Direction of the change")
    percentage: float = Field(..., description="Percentage of change")


class DashboardMetricsRequest(BaseModel):
    """Schema for dashboard metrics request query parameters."""

    client_id: Optional[str] = Field(None, description="Filter metrics by client ID")
    user_id: Optional[str] = Field(None, description="Filter metrics by user ID")
    compare_period: bool = Field(True, description="Include comparison with previous period")
    period: str = Field("last_30_days", description="Time period for metrics calculation")


class DashboardMetricsResponse(BaseModel):
    """Schema for dashboard metrics response."""

    total_claims: int = Field(..., description="Total number of claims")
    open_claims: int = Field(..., description="Number of open claims")
    new_claims_last_period: int = Field(..., description="Number of new claims in the selected period")
    closed_claims_last_period: int = Field(..., description="Number of closed claims in the selected period")
    average_claim_lifecycle_days: Optional[float] = Field(None, description="Average time to close claims in days")
    average_claim_lifecycle_days_change: Optional[MetricChange] = Field(
        None, description="Change in average claim lifecycle compared to previous period"
    )
    total_payments_last_period: Decimal = Field(..., description="Total payments made in the selected period")
    total_payments_change: Optional[MetricChange] = Field(
        None, description="Change in total payments compared to previous period"
    )
    total_outstanding_reserves: Decimal = Field(..., description="Total outstanding reserves")
    total_outstanding_reserves_change: Optional[MetricChange] = Field(
        None, description="Change in outstanding reserves compared to previous period"
    )
    tasks_pending: int = Field(..., description="Number of pending tasks")
    tasks_overdue: int = Field(..., description="Number of overdue tasks")
    fnols_pending: int = Field(..., description="Number of pending FNOLs")
