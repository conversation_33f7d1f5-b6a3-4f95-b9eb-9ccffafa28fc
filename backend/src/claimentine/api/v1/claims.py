"""Claims API endpoints."""

import logging
from typing import List, Optional, Set, Union
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Path, Query, status
from sqlalchemy import select

from claimentine.api.v1.attorneys import api_router as attorneys_router
from claimentine.api.v1.audit import api_router as audit_router
from claimentine.api.v1.deps import (
    get_auto_claim_service,
    get_base_claim_service,
    get_claim_by_identifier,
    get_claim_financials_service,
    get_client_service,
    get_damage_service,
    get_general_liability_claim_service,
    get_property_claim_service,
)
from claimentine.api.v1.documents import api_router as documents_router
from claimentine.api.v1.tasks import task_create_router as tasks_create_router
from claimentine.api.v1.witnesses import api_router as witnesses_router
from claimentine.core.auth import get_current_user
from claimentine.core.exceptions import AuthorizationError, NotFoundError, ValidationError, create_error_detail
from claimentine.models.claim.base import Base<PERSON>laim, ClaimStatus, ClaimType, RecoveryStatus
from claimentine.models.claim.details import BodilyInjuryDetails
from claimentine.models.user import User
from claimentine.schemas.claim.base import (
    ClaimResponseSchema,
    CloseClaimSchema,
    PaginatedClaimResponse,
    RecoveryDetailsResponse,
)
from claimentine.schemas.claim.damage import (
    DamagedPropertyAssetCreate,
    DamagedPropertyAssetResponse,
    DamagedPropertyAssetUpdate,
    DamageInstanceCreate,
    DamageInstanceResponse,
    DamageInstanceUpdate,
)
from claimentine.schemas.claim.details import (
    BodilyInjuryDetailsResponse,
    BodilyInjuryDetailsUpdate,
    InjuredPersonCreate,
    InjuredPersonResponse,
    InjuredPersonUpdate,
    InjuryCreate,
    InjuryResponse,
    InjuryUpdate,
)
from claimentine.schemas.claim.financial import (
    ClaimFinancialsCreate,
    ClaimFinancialsInDB,
    ClaimFinancialsUpdate,
    PaymentCreate,
    PaymentList,
    PaymentResponse,
    ReserveHistoryInDB,
    ReserveUpdate,
)
from claimentine.schemas.claim.types import (
    AutoClaimCreate,
    AutoClaimResponse,
    AutoClaimUpdate,
    GeneralLiabilityClaimCreate,
    GeneralLiabilityClaimResponse,
    GeneralLiabilityClaimUpdate,
    PropertyClaimCreate,
    PropertyClaimResponse,
    PropertyClaimUpdate,
)
from claimentine.services.claim.base import BaseClaimService
from claimentine.services.claim.damage import DamageService
from claimentine.services.claim.financial import ClaimFinancialsService
from claimentine.services.claim.types import AutoClaimService, GeneralLiabilityClaimService, PropertyClaimService
from claimentine.services.client import ClientService

api_router = APIRouter()

# Include documents router
api_router.include_router(documents_router, prefix="/{claim_id}/documents", tags=["documents"])

# Include tasks creation router
api_router.include_router(tasks_create_router, prefix="/{claim_identifier}/tasks", tags=["tasks"])

# Include witnesses router
api_router.include_router(witnesses_router, prefix="/{claim_identifier}/witnesses", tags=["witnesses"])

# Include attorneys router
api_router.include_router(attorneys_router, prefix="/{claim_identifier}/attorneys", tags=["attorneys"])

# Include audit trail router
api_router.include_router(audit_router, prefix="/{claim_identifier}/audit", tags=["audit"])


@api_router.get("", response_model=PaginatedClaimResponse, tags=["claims"])
def list_claims(
    *,
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    status: Optional[ClaimStatus] = None,
    type: Optional[ClaimType] = None,
    client_id: Optional[UUID] = Query(None, description="Filter by client ID"),
    assigned_to_id: Optional[UUID] = None,
    supervisor_id: Optional[UUID] = None,
    team: Optional[str] = None,
    search: Optional[str] = Query(None, description="Search across claim number, claimant name, and description"),
    include: Optional[Set[str]] = Query(
        {"customer"},  # Always include customer by default
        description="Include related items (client, documents, notes, tasks, status_history, financials, assigned_to, supervisor, created_by)",
    ),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
) -> PaginatedClaimResponse:
    """List claims with filtering based on user's permissions."""
    # Add customer to includes if not present
    include = include or set()
    include.add("client")

    claims, total_count = claim_service.list_claims(
        skip=skip,
        limit=limit,
        status=status,
        type=type,
        client_id=client_id,
        assigned_to_id=assigned_to_id,
        supervisor_id=supervisor_id,
        team=team,
        search=search,
        includes=include,
    )

    return PaginatedClaimResponse(items=claims, total=total_count, skip=skip, limit=limit)


@api_router.get(
    "/{claim_identifier}",
    response_model=Union[AutoClaimResponse, PropertyClaimResponse, GeneralLiabilityClaimResponse],
    tags=["claims"],
)
def get_claim(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> Union[AutoClaimResponse, PropertyClaimResponse, GeneralLiabilityClaimResponse]:
    """
    Get claim by ID (UUID) or Claim Number.

    The response model will be determined by the claim's type.
    Optionally include related items via the 'include' query parameter:
    - client: Claim client (included by default if not specified otherwise)
    - documents: Claim documents
    - notes: Claim notes
    - tasks: Claim tasks
    - status_history: Claim status history
    - financials: Claim financials
    - assigned_to: Assigned user details
    - supervisor: Supervisor user details
    - created_by: Creator user details
    - auto_details: Auto claim details (for auto claims)
    - property_details: Property claim details (for property claims)
    - gl_details: General liability claim details (for general liability claims)
    """
    return claim


@api_router.post(
    "",
    response_model=Union[AutoClaimResponse, PropertyClaimResponse, GeneralLiabilityClaimResponse],
    status_code=status.HTTP_201_CREATED,
    tags=["claims"],
)
def create_claim(
    *,
    claim_type: ClaimType = Query(..., description="Type of claim to create"),
    claim_data: dict,  # Accept raw dict instead of Union
    client_service: ClientService = Depends(get_client_service),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
) -> Union[AutoClaimResponse, PropertyClaimResponse, GeneralLiabilityClaimResponse]:
    """Create a new claim."""

    # Parse claim data based on type
    try:
        if claim_type == ClaimType.AUTO:
            parsed_claim_data = AutoClaimCreate(**claim_data)
        elif claim_type == ClaimType.PROPERTY:
            parsed_claim_data = PropertyClaimCreate(**claim_data)
        elif claim_type == ClaimType.GENERAL_LIABILITY:
            parsed_claim_data = GeneralLiabilityClaimCreate(**claim_data)
        else:
            raise ValidationError(f"Unsupported claim type: {claim_type}")
    except Exception as e:
        raise ValidationError(f"Invalid claim data for {claim_type} claim: {str(e)}")

    # Validate client exists and is active
    client = client_service.get_client_by_id(parsed_claim_data.client_id)
    if not client:
        raise NotFoundError(f"Client {parsed_claim_data.client_id} not found")
    if not client.active:
        raise ValidationError(f"Client {client.name} is not active")

    # Create claim using appropriate service
    if claim_type == ClaimType.AUTO:
        claim = auto_claim_service.create_claim(parsed_claim_data)
    elif claim_type == ClaimType.GENERAL_LIABILITY:
        claim = general_liability_claim_service.create_claim(parsed_claim_data)
    else:
        claim = property_claim_service.create_claim(parsed_claim_data)

    return claim


@api_router.patch(
    "/{claim_identifier}",
    response_model=Union[AutoClaimResponse, PropertyClaimResponse, GeneralLiabilityClaimResponse],
    tags=["claims"],
)
def update_claim(
    claim_data: Union[AutoClaimUpdate, PropertyClaimUpdate, GeneralLiabilityClaimUpdate],
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    include: Optional[Set[str]] = Query(
        {"status_history"},
        description="Include related items (documents, notes, tasks, status_history, auto_details, property_details)",
    ),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
) -> Union[AutoClaimResponse, PropertyClaimResponse, GeneralLiabilityClaimResponse]:
    """Update claim by ID or Number."""
    # Resolve identifier first
    try:
        claim_uuid = UUID(claim_identifier)
        base_claim = claim_service.get_claim_by_id(claim_uuid)
    except ValueError:
        base_claim = claim_service.get_claim_by_number(claim_identifier)

    if not base_claim:
        raise NotFoundError(f"Claim {claim_identifier} not found")

    # Validate update data matches claim type
    if base_claim.type == ClaimType.AUTO and not isinstance(claim_data, AutoClaimUpdate):
        raise ValidationError("Update data must be auto claim data for auto claims")
    if base_claim.type == ClaimType.PROPERTY and not isinstance(claim_data, PropertyClaimUpdate):
        raise ValidationError("Update data must be property claim data for property claims")
    if base_claim.type == ClaimType.GENERAL_LIABILITY and not isinstance(claim_data, GeneralLiabilityClaimUpdate):
        raise ValidationError("Update data must be general liability claim data for general liability claims")

    # Update using appropriate service (using the resolved base_claim.id)
    if base_claim.type == ClaimType.AUTO:
        claim = auto_claim_service.update_claim(base_claim.id, claim_data, includes=include)
    elif base_claim.type == ClaimType.GENERAL_LIABILITY:
        claim = general_liability_claim_service.update_claim(base_claim.id, claim_data, includes=include)
    else:
        claim = property_claim_service.update_claim(base_claim.id, claim_data, includes=include)

    return claim


@api_router.delete("/{claim_identifier}", status_code=status.HTTP_204_NO_CONTENT, tags=["claims"])
def delete_claim(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> None:
    """Delete claim by ID or Number."""
    claim_service.delete_claim(claim.id)


@api_router.post(
    "/{claim_identifier}/close",
    response_model=ClaimResponseSchema,
    tags=["claims"],
)
def close_claim_endpoint(
    *,
    claim_data: CloseClaimSchema,
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    current_user: User = Depends(get_current_user),
) -> ClaimResponseSchema:
    """Close a claim identified by ID or Number."""
    updated_claim = claim_service.close_claim(claim_id=claim.id, status=claim_data.status)
    return updated_claim


@api_router.get("/{claim_identifier}/financials", response_model=Optional[ClaimFinancialsInDB], tags=["claims"])
def get_claim_financials(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    financials_service: ClaimFinancialsService = Depends(get_claim_financials_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> Optional[ClaimFinancialsInDB]:
    """Get financials for a claim identified by ID or Number."""
    financials = financials_service.get_financials(claim.id)
    if not financials:
        raise NotFoundError(f"No financials found for claim {claim_identifier}")
    return financials


@api_router.post(
    "/{claim_identifier}/financials",
    response_model=ClaimFinancialsInDB,
    status_code=status.HTTP_201_CREATED,
    tags=["claims"],
)
def create_claim_financials(
    data: ClaimFinancialsCreate,
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    financials_service: ClaimFinancialsService = Depends(get_claim_financials_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> ClaimFinancialsInDB:
    """Create financials for a claim identified by ID or Number."""
    # Ensure create_financials uses claim.id
    return financials_service.create_financials(claim.id, data)


@api_router.put("/{claim_identifier}/financials", response_model=ClaimFinancialsInDB, tags=["claims"])
def update_claim_financials(
    data: ClaimFinancialsUpdate,
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    financials_service: ClaimFinancialsService = Depends(get_claim_financials_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> ClaimFinancialsInDB:
    """Update financial details for a claim identified by ID or Number."""
    return financials_service.update_financials(claim.id, data)


@api_router.put("/{claim_identifier}/financials/reserve", response_model=ClaimFinancialsInDB, tags=["claims"])
def update_claim_reserve(
    data: ReserveUpdate,
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    financials_service: ClaimFinancialsService = Depends(get_claim_financials_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> ClaimFinancialsInDB:
    """Update reserve for a claim identified by ID or Number."""
    # Ensure update_reserve uses claim.id
    try:
        # This should raise AuthorizationError if amount exceeds threshold
        return financials_service.update_reserve(claim.id, data.reserve_type, data.amount, data.notes)
    except AuthorizationError as e:
        # Log the error
        logging.error(
            f"Authorization error in update_claim_reserve: {str(e)}",
            extra={
                "claim_id": str(claim.id),
                "reserve_type": str(data.reserve_type),
                "amount": str(data.amount),
            },
        )
        # Re-raise the original AuthorizationError - it's already an AppException
        # which will be handled by the global handler in main.py
        raise


@api_router.get(
    "/{claim_identifier}/financials/reserve-history", response_model=List[ReserveHistoryInDB], tags=["claims"]
)
def get_claim_reserve_history(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    financials_service: ClaimFinancialsService = Depends(get_claim_financials_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> List[ReserveHistoryInDB]:
    """Get reserve history for a claim identified by ID or Number."""
    return financials_service.get_reserve_history(claim.id)


@api_router.post("/{claim_identifier}/financials/payments", response_model=PaymentResponse, tags=["claims", "payments"])
def add_claim_payment(
    payment_data: PaymentCreate,
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    financials_service: ClaimFinancialsService = Depends(get_claim_financials_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> PaymentResponse:
    """Add a payment to a claim identified by ID or Number."""
    return financials_service.add_payment(claim.id, payment_data)


@api_router.get("/{claim_identifier}/financials/payments", response_model=PaymentList, tags=["claims", "payments"])
def list_claim_payments(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    financials_service: ClaimFinancialsService = Depends(get_claim_financials_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> PaymentList:
    """List payments for a claim identified by ID or Number."""
    payments, total = financials_service.list_payments(claim.id, skip, limit)
    return PaymentList(items=payments, total=total)


@api_router.patch(
    "/{claim_identifier}/recovery/status", response_model=ClaimResponseSchema, tags=["claims", "recovery"]
)
def update_claim_recovery_status(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    recovery_status: RecoveryStatus = Query(..., description="New recovery status"),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> ClaimResponseSchema:
    """Update recovery status for a claim identified by ID or Number."""
    return claim_service.update_recovery_status(claim.id, recovery_status)


@api_router.get("/{claim_identifier}/recovery", response_model=RecoveryDetailsResponse, tags=["claims", "recovery"])
def get_claim_recovery_details(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> RecoveryDetailsResponse:
    """Get recovery details for a claim identified by ID or Number."""
    return RecoveryDetailsResponse(
        recovery_status=claim.recovery_status,
        carrier_name=claim.carrier_name,
        carrier_contact=claim.carrier_contact,
        carrier_claim_number=claim.carrier_claim_number,
        carrier_adjuster=claim.carrier_adjuster,
        expected_amount=claim.expected_amount,
        received_amount=claim.received_amount,
        claim_id=claim.id,
        claim_number=claim.claim_number,
    )


@api_router.patch("/{claim_identifier}/carrier", response_model=ClaimResponseSchema, tags=["claims", "recovery"])
def update_claim_carrier_details(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    carrier_name: Optional[str] = Query(None, description="Name of the third-party carrier"),
    carrier_contact: Optional[str] = Query(None, description="Contact info for the third-party carrier"),
    carrier_claim_number: Optional[str] = Query(None, description="Third-party carrier's claim number"),
    carrier_adjuster: Optional[str] = Query(None, description="Third-party carrier's adjuster name"),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> ClaimResponseSchema:
    """Update third-party carrier information for a claim identified by ID or Number."""
    return claim_service.update_carrier_details(
        claim.id, carrier_name, carrier_contact, carrier_claim_number, carrier_adjuster
    )


@api_router.patch(
    "/{claim_identifier}/recovery/amounts", response_model=ClaimResponseSchema, tags=["claims", "recovery"]
)
def update_claim_recovery_amounts(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    expected_amount: Optional[str] = Query(None, description="Expected recovery amount"),
    received_amount: Optional[str] = Query(None, description="Amount actually recovered"),
    claim_service: BaseClaimService = Depends(get_base_claim_service),
    claim: BaseClaim = Depends(get_claim_by_identifier),
) -> ClaimResponseSchema:
    """Update recovery amounts for a claim identified by ID or Number."""
    return claim_service.update_recovery_amounts(claim.id, expected_amount, received_amount)


@api_router.get(
    "/{claim_identifier}/bodily_injury",
    response_model=Optional[BodilyInjuryDetailsResponse],
    tags=["claims", "bodily-injury"],
)
def get_bodily_injury_details(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> Optional[BodilyInjuryDetailsResponse]:
    """Get bodily injury details for a claim identified by ID or Number."""
    # The claim dependency already does the basic permission check

    # Try to retrieve bodily injury details based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            return None
        bodily_injury = auto_claim_service.get_bodily_injury_details(claim.id)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            return None
        bodily_injury = general_liability_claim_service.get_bodily_injury_details(claim.id)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            return None
        bodily_injury = property_claim_service.get_bodily_injury_details(claim.id)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support bodily injury details")

    return bodily_injury


@api_router.put(
    "/{claim_identifier}/bodily_injury",
    response_model=BodilyInjuryDetailsResponse,
    tags=["claims", "bodily-injury"],
)
def update_bodily_injury_details(
    bodily_injury_data: BodilyInjuryDetailsUpdate,
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> BodilyInjuryDetailsResponse:
    """Update bodily injury details for a claim identified by ID or Number."""
    # The claim dependency already handles basic permission check

    # Update bodily injury details based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise ValidationError("Cannot update bodily injury details: auto details not found")

        # First check if bodily injury details exist
        existing_details = auto_claim_service.get_bodily_injury_details(claim.id)
        if existing_details:
            # Update existing bodily injury details
            bodily_injury = auto_claim_service.update_bodily_injury_details(claim.id, bodily_injury_data)
        else:
            # Create new bodily injury details
            bodily_injury = auto_claim_service.create_bodily_injury_details(claim.id, bodily_injury_data)

    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise ValidationError("Cannot update bodily injury details: GL details not found")

        # First check if bodily injury details exist
        existing_details = general_liability_claim_service.get_bodily_injury_details(claim.id)
        if existing_details:
            # Update existing bodily injury details
            bodily_injury = general_liability_claim_service.update_bodily_injury_details(claim.id, bodily_injury_data)
        else:
            # Create new bodily injury details
            bodily_injury = general_liability_claim_service.create_bodily_injury_details(claim.id, bodily_injury_data)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise ValidationError("Cannot update bodily injury details: property details not found")

        # First check if bodily injury details exist
        existing_details = property_claim_service.get_bodily_injury_details(claim.id)
        if existing_details:
            # Update existing bodily injury details
            bodily_injury = property_claim_service.update_bodily_injury_details(claim.id, bodily_injury_data)
        else:
            # Create new bodily injury details
            bodily_injury = property_claim_service.create_bodily_injury_details(claim.id, bodily_injury_data)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support bodily injury details")

    return bodily_injury


@api_router.delete(
    "/{claim_identifier}/bodily_injury",
    status_code=status.HTTP_204_NO_CONTENT,
    tags=["claims", "bodily-injury"],
)
def delete_bodily_injury_details(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> None:
    """Delete bodily injury details for a claim identified by ID or Number."""
    # The claim dependency already handles basic permission check

    # Delete bodily injury details based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise ValidationError("Cannot delete bodily injury details: auto details not found")
        auto_claim_service.delete_bodily_injury_details(claim.id)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise ValidationError("Cannot delete bodily injury details: GL details not found")
        general_liability_claim_service.delete_bodily_injury_details(claim.id)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise ValidationError("Cannot delete bodily injury details: property details not found")
        property_claim_service.delete_bodily_injury_details(claim.id)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support bodily injury details")


# New endpoints for injured persons


@api_router.get(
    "/{claim_identifier}/injured-persons",
    response_model=List[InjuredPersonResponse],
    tags=["claims", "bodily-injury"],
)
def get_injured_persons(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> List[InjuredPersonResponse]:
    """Get all injured persons for a claim identified by ID or Number."""
    # The claim dependency already does the basic permission check

    # Try to retrieve injured persons based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            return []
        return auto_claim_service.get_injured_persons(claim.id)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            return []
        return general_liability_claim_service.get_injured_persons(claim.id)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            return []
        return property_claim_service.get_injured_persons(claim.id)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injured persons")


@api_router.get(
    "/{claim_identifier}/injured-persons/{person_id}",
    response_model=InjuredPersonResponse,
    tags=["claims", "bodily-injury"],
)
def get_injured_person(
    person_id: UUID = Path(..., description="Injured Person ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> InjuredPersonResponse:
    """Get an injured person for a claim identified by ID or Number."""
    # The claim dependency already does the basic permission check

    # Try to retrieve injured person based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise NotFoundError("Auto details not found")
        person = auto_claim_service.get_injured_person(claim.id, person_id)
        if not person:
            raise NotFoundError(f"Injured person {person_id} not found")
        return person
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise NotFoundError("GL details not found")
        person = general_liability_claim_service.get_injured_person(claim.id, person_id)
        if not person:
            raise NotFoundError(f"Injured person {person_id} not found")
        return person
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise NotFoundError("Property details not found")
        person = property_claim_service.get_injured_person(claim.id, person_id)
        if not person:
            raise NotFoundError(f"Injured person {person_id} not found")
        return person
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injured persons")


@api_router.post(
    "/{claim_identifier}/injured-persons",
    response_model=InjuredPersonResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["claims", "bodily-injury"],
)
def create_injured_person(
    person_data: InjuredPersonCreate,
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> InjuredPersonResponse:
    """Create an injured person for a claim identified by ID or Number."""
    # The claim dependency already handles basic permission check

    # Create injured person based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise ValidationError("Cannot create injured person: auto details not found")
        return auto_claim_service.create_injured_person(claim.id, person_data)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise ValidationError("Cannot create injured person: GL details not found")
        return general_liability_claim_service.create_injured_person(claim.id, person_data)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise ValidationError("Cannot create injured person: property details not found")
        return property_claim_service.create_injured_person(claim.id, person_data)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injured persons")


@api_router.put(
    "/{claim_identifier}/injured-persons/{person_id}",
    response_model=InjuredPersonResponse,
    tags=["claims", "bodily-injury"],
)
def update_injured_person(
    person_data: InjuredPersonUpdate,
    person_id: UUID = Path(..., description="Injured Person ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> InjuredPersonResponse:
    """Update an injured person for a claim identified by ID or Number."""
    # The claim dependency already handles basic permission check

    # Update injured person based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise ValidationError("Cannot update injured person: auto details not found")
        return auto_claim_service.update_injured_person(claim.id, person_id, person_data)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise ValidationError("Cannot update injured person: GL details not found")
        return general_liability_claim_service.update_injured_person(claim.id, person_id, person_data)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise ValidationError("Cannot update injured person: property details not found")
        return property_claim_service.update_injured_person(claim.id, person_id, person_data)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injured persons")


@api_router.delete(
    "/{claim_identifier}/injured-persons/{person_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    tags=["claims", "bodily-injury"],
)
def delete_injured_person(
    person_id: UUID = Path(..., description="Injured Person ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> None:
    """Delete an injured person for a claim identified by ID or Number."""
    # The claim dependency already handles basic permission check

    # Delete injured person based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise ValidationError("Cannot delete injured person: auto details not found")
        auto_claim_service.delete_injured_person(claim.id, person_id)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise ValidationError("Cannot delete injured person: GL details not found")
        general_liability_claim_service.delete_injured_person(claim.id, person_id)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise ValidationError("Cannot delete injured person: property details not found")
        property_claim_service.delete_injured_person(claim.id, person_id)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injured persons")


# New endpoints for injuries


@api_router.get(
    "/{claim_identifier}/injured-persons/{person_id}/injuries",
    response_model=List[InjuryResponse],
    tags=["claims", "bodily-injury"],
)
def get_injuries(
    person_id: UUID = Path(..., description="Injured Person ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> List[InjuryResponse]:
    """Get all injuries for an injured person."""
    # The claim dependency already does the basic permission check

    # Try to retrieve injuries based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            return []
        return auto_claim_service.get_injuries(claim.id, person_id)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            return []
        return general_liability_claim_service.get_injuries(claim.id, person_id)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            return []
        return property_claim_service.get_injuries(claim.id, person_id)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injuries")


@api_router.get(
    "/{claim_identifier}/injured-persons/{person_id}/injuries/{injury_id}",
    response_model=InjuryResponse,
    tags=["claims", "bodily-injury"],
)
def get_injury(
    person_id: UUID = Path(..., description="Injured Person ID"),
    injury_id: UUID = Path(..., description="Injury ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> InjuryResponse:
    """Get an injury for an injured person."""
    # The claim dependency already does the basic permission check

    # Try to retrieve injury based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise NotFoundError("Auto details not found")
        injury = auto_claim_service.get_injury(claim.id, person_id, injury_id)
        if not injury:
            raise NotFoundError(f"Injury {injury_id} not found")
        return injury
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise NotFoundError("GL details not found")
        injury = general_liability_claim_service.get_injury(claim.id, person_id, injury_id)
        if not injury:
            raise NotFoundError(f"Injury {injury_id} not found")
        return injury
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise NotFoundError("Property details not found")
        injury = property_claim_service.get_injury(claim.id, person_id, injury_id)
        if not injury:
            raise NotFoundError(f"Injury {injury_id} not found")
        return injury
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injuries")


@api_router.post(
    "/{claim_identifier}/injured-persons/{person_id}/injuries",
    response_model=InjuryResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["claims", "bodily-injury"],
)
def create_injury(
    injury_data: InjuryCreate,
    person_id: UUID = Path(..., description="Injured Person ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> InjuryResponse:
    """Create an injury for an injured person."""
    # The claim dependency already handles basic permission check

    # Create injury based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise ValidationError("Cannot create injury: auto details not found")
        return auto_claim_service.create_injury(claim.id, person_id, injury_data)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise ValidationError("Cannot create injury: GL details not found")
        return general_liability_claim_service.create_injury(claim.id, person_id, injury_data)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise ValidationError("Cannot create injury: property details not found")
        return property_claim_service.create_injury(claim.id, person_id, injury_data)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injuries")


@api_router.put(
    "/{claim_identifier}/injured-persons/{person_id}/injuries/{injury_id}",
    response_model=InjuryResponse,
    tags=["claims", "bodily-injury"],
)
def update_injury(
    injury_data: InjuryUpdate,
    person_id: UUID = Path(..., description="Injured Person ID"),
    injury_id: UUID = Path(..., description="Injury ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> InjuryResponse:
    """Update an injury for an injured person."""
    # The claim dependency already handles basic permission check

    # Update injury based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise ValidationError("Cannot update injury: auto details not found")
        return auto_claim_service.update_injury(claim.id, person_id, injury_id, injury_data)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise ValidationError("Cannot update injury: GL details not found")
        return general_liability_claim_service.update_injury(claim.id, person_id, injury_id, injury_data)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise ValidationError("Cannot update injury: property details not found")
        return property_claim_service.update_injury(claim.id, person_id, injury_id, injury_data)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injuries")


@api_router.delete(
    "/{claim_identifier}/injured-persons/{person_id}/injuries/{injury_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    tags=["claims", "bodily-injury"],
)
def delete_injury(
    person_id: UUID = Path(..., description="Injured Person ID"),
    injury_id: UUID = Path(..., description="Injury ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    auto_claim_service: AutoClaimService = Depends(get_auto_claim_service),
    general_liability_claim_service: GeneralLiabilityClaimService = Depends(get_general_liability_claim_service),
    property_claim_service: PropertyClaimService = Depends(get_property_claim_service),
) -> None:
    """Delete an injury for an injured person."""
    # The claim dependency already handles basic permission check

    # Delete injury based on claim type
    if claim.type == ClaimType.AUTO:
        if not claim.auto_details:
            raise ValidationError("Cannot delete injury: auto details not found")
        auto_claim_service.delete_injury(claim.id, person_id, injury_id)
    elif claim.type == ClaimType.GENERAL_LIABILITY:
        if not claim.gl_details:
            raise ValidationError("Cannot delete injury: GL details not found")
        general_liability_claim_service.delete_injury(claim.id, person_id, injury_id)
    elif claim.type == ClaimType.PROPERTY:
        if not claim.property_details:
            raise ValidationError("Cannot delete injury: property details not found")
        property_claim_service.delete_injury(claim.id, person_id, injury_id)
    else:
        raise ValidationError(f"Claim type {claim.type} does not support injuries")


# Endpoints for damaged property assets


@api_router.get(
    "/{claim_identifier}/damaged-property-assets",
    response_model=List[DamagedPropertyAssetResponse],
    tags=["claims", "property-damage"],
)
def get_damaged_property_assets(
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> List[DamagedPropertyAssetResponse]:
    """Get all damaged property assets for a claim identified by ID or Number."""
    # The claim dependency already does the basic permission check
    return damage_service.get_damaged_property_assets(claim.id, current_user.id)


@api_router.get(
    "/{claim_identifier}/damaged-property-assets/{asset_id}",
    response_model=DamagedPropertyAssetResponse,
    tags=["claims", "property-damage"],
)
def get_damaged_property_asset(
    asset_id: UUID = Path(..., description="Damaged Property Asset ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> DamagedPropertyAssetResponse:
    """Get a damaged property asset for a claim identified by ID or Number."""
    # The claim dependency already does the basic permission check

    asset = damage_service.get_damaged_property_asset(claim.id, asset_id, current_user.id)
    if not asset:
        raise NotFoundError(f"Damaged property asset {asset_id} not found")
    return asset


@api_router.post(
    "/{claim_identifier}/damaged-property-assets",
    response_model=DamagedPropertyAssetResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["claims", "property-damage"],
)
def create_damaged_property_asset(
    asset_data: DamagedPropertyAssetCreate,
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> DamagedPropertyAssetResponse:
    """Create a damaged property asset for a claim identified by ID or Number."""
    # The claim dependency already handles basic permission check
    return damage_service.create_damaged_property_asset(claim.id, asset_data, current_user.id)


@api_router.put(
    "/{claim_identifier}/damaged-property-assets/{asset_id}",
    response_model=DamagedPropertyAssetResponse,
    tags=["claims", "property-damage"],
)
def update_damaged_property_asset(
    asset_data: DamagedPropertyAssetUpdate,
    asset_id: UUID = Path(..., description="Damaged Property Asset ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> DamagedPropertyAssetResponse:
    """Update a damaged property asset for a claim identified by ID or Number."""
    # The claim dependency already handles basic permission check
    return damage_service.update_damaged_property_asset(claim.id, asset_id, asset_data, current_user.id)


@api_router.delete(
    "/{claim_identifier}/damaged-property-assets/{asset_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    tags=["claims", "property-damage"],
)
def delete_damaged_property_asset(
    asset_id: UUID = Path(..., description="Damaged Property Asset ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> None:
    """Delete a damaged property asset for a claim identified by ID or Number."""
    # The claim dependency already handles basic permission check
    damage_service.delete_damaged_property_asset(claim.id, asset_id, current_user.id)


# Endpoints for damage instances


@api_router.get(
    "/{claim_identifier}/damaged-property-assets/{asset_id}/damage-instances",
    response_model=List[DamageInstanceResponse],
    tags=["claims", "property-damage"],
)
def get_damage_instances(
    asset_id: UUID = Path(..., description="Damaged Property Asset ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> List[DamageInstanceResponse]:
    """Get all damage instances for a damaged property asset."""
    # The claim dependency already does the basic permission check
    return damage_service.get_damage_instances(claim.id, asset_id, current_user.id)


@api_router.get(
    "/{claim_identifier}/damaged-property-assets/{asset_id}/damage-instances/{instance_id}",
    response_model=DamageInstanceResponse,
    tags=["claims", "property-damage"],
)
def get_damage_instance(
    asset_id: UUID = Path(..., description="Damaged Property Asset ID"),
    instance_id: UUID = Path(..., description="Damage Instance ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> DamageInstanceResponse:
    """Get a damage instance for a damaged property asset."""
    # The claim dependency already does the basic permission check

    instance = damage_service.get_damage_instance(claim.id, asset_id, instance_id, current_user.id)
    if not instance:
        raise NotFoundError(f"Damage instance {instance_id} not found")
    return instance


@api_router.post(
    "/{claim_identifier}/damaged-property-assets/{asset_id}/damage-instances",
    response_model=DamageInstanceResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["claims", "property-damage"],
)
def create_damage_instance(
    instance_data: DamageInstanceCreate,
    asset_id: UUID = Path(..., description="Damaged Property Asset ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> DamageInstanceResponse:
    """Create a damage instance for a damaged property asset."""
    # The claim dependency already handles basic permission check
    return damage_service.create_damage_instance(claim.id, asset_id, instance_data, current_user.id)


@api_router.put(
    "/{claim_identifier}/damaged-property-assets/{asset_id}/damage-instances/{instance_id}",
    response_model=DamageInstanceResponse,
    tags=["claims", "property-damage"],
)
def update_damage_instance(
    instance_data: DamageInstanceUpdate,
    asset_id: UUID = Path(..., description="Damaged Property Asset ID"),
    instance_id: UUID = Path(..., description="Damage Instance ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> DamageInstanceResponse:
    """Update a damage instance for a damaged property asset."""
    # The claim dependency already handles basic permission check
    return damage_service.update_damage_instance(claim.id, asset_id, instance_id, instance_data, current_user.id)


@api_router.delete(
    "/{claim_identifier}/damaged-property-assets/{asset_id}/damage-instances/{instance_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    tags=["claims", "property-damage"],
)
def delete_damage_instance(
    asset_id: UUID = Path(..., description="Damaged Property Asset ID"),
    instance_id: UUID = Path(..., description="Damage Instance ID"),
    claim_identifier: str = Path(..., description="Claim ID (UUID) or Claim Number"),
    claim: BaseClaim = Depends(get_claim_by_identifier),
    damage_service: DamageService = Depends(get_damage_service),
    current_user: User = Depends(get_current_user),
) -> None:
    """Delete a damage instance for a damaged property asset."""
    # The claim dependency already handles basic permission check
    damage_service.delete_damage_instance(claim.id, asset_id, instance_id, current_user.id)
