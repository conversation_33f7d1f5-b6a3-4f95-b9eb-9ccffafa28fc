'use client'

import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { ChevronRight, PlusIcon, Pencil } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useDateFormatter } from '@/hooks/useDateFormatter'

interface ClientUsersProps {
  clientId: string
}

export function ClientUsers({ clientId }: ClientUsersProps) {
  const { formatDateTime } = useDateFormatter()

  // This would fetch users from the API filtered by customer ID
  // For now, we'll use mock data
  const users = [
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'ADMIN',
      status: 'ACTIVE',
      lastLogin: new Date('2023-04-15T09:30:00'),
      avatar: '/placeholder-user.jpg',
      initials: '<PERSON><PERSON>',
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      role: 'ADJUSTER',
      status: 'ACTIVE',
      lastLogin: new Date('2023-04-14T14:15:00'),
      avatar: '/placeholder-user.jpg',
      initials: 'SM',
    },
    {
      id: '3',
      name: 'Robert Davis',
      email: '<EMAIL>',
      role: 'MANAGER',
      status: 'ACTIVE',
      lastLogin: new Date('2023-04-15T11:45:00'),
      avatar: '/placeholder-user.jpg',
      initials: 'RD',
    },
    {
      id: '4',
      name: 'Emily Wilson',
      email: '<EMAIL>',
      role: 'ADJUSTER',
      status: 'ACTIVE',
      lastLogin: new Date('2023-04-13T16:20:00'),
      avatar: '/placeholder-user.jpg',
      initials: 'EW',
    },
    {
      id: '5',
      name: 'Michael Brown',
      email: '<EMAIL>',
      role: 'READONLY',
      status: 'INACTIVE',
      lastLogin: new Date('2023-03-25T10:30:00'),
      avatar: '/placeholder-user.jpg',
      initials: 'MB',
    },
  ]

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'MANAGER':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'ADJUSTER':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'READONLY':
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Client Users</CardTitle>
        <Button className="flex items-center gap-1">
          <PlusIcon className="h-4 w-4" />
          Add User
        </Button>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Login</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map(user => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.avatar || '/placeholder.svg'} alt={user.name} />
                        <AvatarFallback>{user.initials}</AvatarFallback>
                      </Avatar>
                      <span className="font-medium">{user.name}</span>
                    </div>
                  </TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getRoleColor(user.role)}>
                      {user.role}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getStatusColor(user.status)}>
                      {user.status}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm text-muted-foreground">
                    {formatDateTime(user.lastLogin.toISOString())}{' '}
                    <span className="text-xs">(example)</span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Button size="icon" variant="ghost" className="h-8 w-8" title="Edit User">
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button size="icon" variant="ghost" className="h-8 w-8" title="View Details">
                        <ChevronRight className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
