'use client'

import type React from 'react'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { CalendarIcon, Check, ChevronsUpDown, Search, Clock } from 'lucide-react'
import { format } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from '@/components/ui/command'
import { api } from '@/lib/api'
import {
  FNOLCreate as ApiFNOLCreate,
  ClientResponse,
  FNOLResponse,
  PaginatedResponse,
  ReporterRelationship,
} from '@/lib/api/types'
import { USState } from '@/lib/api/types/shared-types'
import { useApi } from '@/hooks/useApi'
import { toast } from '@/components/ui/use-toast'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Terminal } from 'lucide-react'

// Frontend representation with updated fields
interface FrontendFNOLFormState {
  // Reporter information
  reporter_name?: string
  reporter_phone?: string
  reporter_email?: string
  reporter_relationship?: ReporterRelationship

  // Incident details
  incident_date?: string
  incident_time?: string // 12-hour format (HH:MM AM/PM)
  incident_state?: USState
  incident_description?: string
  incident_location?: string

  // Policy info
  client_id?: string
  policy_number?: string
}

interface FieldValidationState {
  // Reporter information
  reporter_name?: string
  reporter_phone?: string
  reporter_email?: string
  contact_fields?: string // Combined validation for phone/email
  reporter_relationship?: string

  // Incident details
  incident_date?: string
  incident_time?: string
  incident_state?: string
  incident_description?: string
  incident_location?: string

  // Policy info
  client_id?: string
  policy_number?: string
}

export function FnolCreationForm() {
  const router = useRouter()

  const [formData, setFormData] = useState<FrontendFNOLFormState>({
    reporter_name: '',
    reporter_phone: '',
    reporter_email: '',
    reporter_relationship: undefined,
    incident_date: new Date().toISOString().split('T')[0],
    incident_time: '',
    incident_state: undefined,
    incident_description: '',
    incident_location: '',
    client_id: undefined,
    policy_number: '',
  })

  // Add field-specific validation errors
  const [validationErrors, setValidationErrors] = useState<FieldValidationState>({})

  const {
    data: clientsData,
    isLoading: isLoadingClients,
    error: errorClients,
  } = useApi<PaginatedResponse<ClientResponse>>(() => api.clients.getClients(), [])
  const clients: ClientResponse[] = clientsData?.items ?? []

  const [clientSearch, setClientSearch] = useState('')
  const [openClientSelect, setOpenClientSelect] = useState(false)

  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)

  // Validate incident date on change
  const validateIncidentDate = (dateStr: string): string | undefined => {
    if (!dateStr) return 'Incident date is required'

    const selectedDate = new Date(dateStr)
    const today = new Date()

    // Reset time to compare only the dates
    today.setHours(0, 0, 0, 0)
    selectedDate.setHours(0, 0, 0, 0)

    if (selectedDate > today) {
      return 'Incident date cannot be in the future'
    }

    if (selectedDate.getFullYear() < 1900) {
      return 'Incident date cannot be before 1900'
    }

    return undefined
  }

  // Validate phone number format
  const validatePhoneNumber = (phone: string): string | undefined => {
    if (!phone.trim()) return undefined // Optional field

    // Remove all non-digit characters for validation
    const digitsOnly = phone.replace(/[^\d]/g, '')

    // Check if it's a valid US phone number (10 digits, optionally with country code)
    if (digitsOnly.length === 10) {
      return undefined // Valid 10-digit US number
    } else if (digitsOnly.length === 11 && digitsOnly.startsWith('1')) {
      return undefined // Valid US number with country code
    } else {
      return 'Please enter a valid US phone number (e.g., ************)'
    }
  }

  // Validate email address format
  const validateEmailAddress = (email: string): string | undefined => {
    if (!email.trim()) return undefined // Optional field

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailPattern.test(email)) {
      return 'Please enter a valid email address'
    }
    return undefined
  }

  // Validate that at least one contact method is provided
  const validateContactFields = (phone?: string, email?: string): string | undefined => {
    const hasPhone = phone && phone.trim().length > 0
    const hasEmail = email && email.trim().length > 0

    if (!hasPhone && !hasEmail) {
      return 'At least one contact method (phone or email) is required'
    }
    return undefined
  }

  // Validate incident state
  const validateIncidentState = (state?: USState): string | undefined => {
    if (!state) return 'Incident state is required'
    return undefined
  }

  // Validate incident time (12-hour format)
  const validateIncidentTime = (time?: string): string | undefined => {
    if (!time || !time.trim()) return undefined // Optional field

    // Validate 12-hour format (HH:MM AM/PM)
    const timePattern = /^(0?[1-9]|1[0-2]):[0-5][0-9]\s?(AM|PM)$/i
    if (!timePattern.test(time.trim())) {
      return 'Please enter time in 12-hour format (e.g., 2:30 PM)'
    }
    return undefined
  }

  // Convert 12-hour time to 24-hour format for API
  const convertTo24Hour = (time12h: string): string => {
    if (!time12h.trim()) return ''

    const [time, modifier] = time12h.trim().split(/\s+/)
    let [hours, minutes] = time.split(':')

    if (hours === '12') {
      hours = '00'
    }

    if (modifier.toUpperCase() === 'PM') {
      hours = (parseInt(hours, 10) + 12).toString()
    }

    return `${hours.padStart(2, '0')}:${minutes}:00`
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))

    // Clear validation error when user types
    setValidationErrors(prev => ({ ...prev, [name]: undefined, contact_fields: undefined }))

    // Special validation for incident date
    if (name === 'incident_date') {
      const dateError = validateIncidentDate(value)
      if (dateError) {
        setValidationErrors(prev => ({ ...prev, incident_date: dateError }))
      }
    }

    // Special validation for phone
    if (name === 'reporter_phone') {
      const phoneError = validatePhoneNumber(value)
      if (phoneError) {
        setValidationErrors(prev => ({ ...prev, reporter_phone: phoneError }))
      }
    }

    // Special validation for email
    if (name === 'reporter_email') {
      const emailError = validateEmailAddress(value)
      if (emailError) {
        setValidationErrors(prev => ({ ...prev, reporter_email: emailError }))
      }
    }

    // Special validation for incident time
    if (name === 'incident_time') {
      const timeError = validateIncidentTime(value)
      if (timeError) {
        setValidationErrors(prev => ({ ...prev, incident_time: timeError }))
      }
    }
  }

  const handleSelectChange = (name: string, value: string | undefined) => {
    setFormData(prev => ({ ...prev, [name]: value }))
    setValidationErrors(prev => ({ ...prev, [name]: undefined }))
  }

  const handleClientSelect = (clientId: string) => {
    setFormData(prev => ({ ...prev, client_id: clientId }))
    setValidationErrors(prev => ({ ...prev, client_id: undefined }))
    setOpenClientSelect(false)
  }

  const validateForm = (): boolean => {
    const errors: FieldValidationState = {}
    let isValid = true

    // Validate required fields
    if (!formData.reporter_name?.trim()) {
      errors.reporter_name = 'Reporter name is required'
      isValid = false
    }

    // Validate contact fields (at least one required)
    const contactError = validateContactFields(formData.reporter_phone, formData.reporter_email)
    if (contactError) {
      errors.contact_fields = contactError
      isValid = false
    }

    // Validate individual contact fields if provided
    if (formData.reporter_phone) {
      const phoneError = validatePhoneNumber(formData.reporter_phone)
      if (phoneError) {
        errors.reporter_phone = phoneError
        isValid = false
      }
    }

    if (formData.reporter_email) {
      const emailError = validateEmailAddress(formData.reporter_email)
      if (emailError) {
        errors.reporter_email = emailError
        isValid = false
      }
    }

    // Validate incident details
    if (!formData.incident_description?.trim()) {
      errors.incident_description = 'Incident description is required'
      isValid = false
    }

    const stateError = validateIncidentState(formData.incident_state)
    if (stateError) {
      errors.incident_state = stateError
      isValid = false
    }

    if (formData.incident_time) {
      const timeError = validateIncidentTime(formData.incident_time)
      if (timeError) {
        errors.incident_time = timeError
        isValid = false
      }
    }

    // Validate client selection
    if (!formData.client_id) {
      errors.client_id = 'Client is required'
      isValid = false
    }

    // Validate incident date
    const dateError = validateIncidentDate(formData.incident_date || '')
    if (dateError) {
      errors.incident_date = dateError
      isValid = false
    }

    setValidationErrors(errors)
    return isValid
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate the form first
    if (!validateForm()) {
      // Don't submit if validation fails
      return
    }

    // Map frontend state to the backend API's FNOLCreate structure
    const payload: ApiFNOLCreate = {
      reported_by: formData.reporter_name || '',
      reporter_phone: formData.reporter_phone?.trim() || null,
      reporter_email: formData.reporter_email?.trim() || null,
      reporter_relationship: formData.reporter_relationship || null,
      incident_date: formData.incident_date || null,
      incident_time: formData.incident_time ? convertTo24Hour(formData.incident_time) : null,
      incident_state: formData.incident_state || USState.UNKNOWN,
      incident_location: formData.incident_location?.trim() || null,
      description: formData.incident_description || '',
      client_id: formData.client_id || '',
      policy_number: formData.policy_number?.trim() || null,
    }

    setIsSubmitting(true)
    setSubmitError(null)

    try {
      const createdFnol: FNOLResponse = await api.fnol.createFNOL(payload)
      toast({
        title: 'FNOL Created',
        description: `FNOL ${createdFnol.fnol_number} successfully created.`,
      })
      router.push('/fnol')
    } catch (error: any) {
      console.error('FNOL Creation Error:', error)
      // Handle potential validation errors from the API
      if (error.response?.data?.detail) {
        // Handle structured validation errors if available
        const apiErrors = error.response.data.detail
        if (typeof apiErrors === 'object' && apiErrors !== null) {
          // Map API validation errors to form fields
          const fieldErrors: FieldValidationState = {}

          Object.entries(apiErrors).forEach(([key, value]) => {
            // Convert snake_case to camelCase if needed
            fieldErrors[key as keyof FieldValidationState] = Array.isArray(value)
              ? value[0]
              : String(value)
          })

          setValidationErrors(prev => ({ ...prev, ...fieldErrors }))
        } else {
          // Simple string error
          setSubmitError(error.response.data.detail)
        }
      } else {
        const errorMessage = error.message || 'An unknown error occurred while submitting.'
        setSubmitError(errorMessage)
      }

      toast({
        variant: 'destructive',
        title: 'Failed to Create FNOL',
        description: error.message || 'An unknown error occurred while submitting.',
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(clientSearch.toLowerCase())
  )

  // Create sorted state options for dropdown
  const stateOptions = [
    ...Object.values(USState)
      .filter(state => state !== USState.UNKNOWN)
      .sort(),
    USState.UNKNOWN, // Add Unknown at the end
  ]

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {submitError && (
        <Alert variant="destructive">
          <Terminal className="h-4 w-4" />
          <AlertTitle>Submission Error</AlertTitle>
          <AlertDescription>
            {submitError || 'An unexpected error occurred while submitting.'}
          </AlertDescription>
        </Alert>
      )}

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Reporter Information</h3>

        {/* Contact fields validation message */}
        {validationErrors.contact_fields && (
          <Alert variant="destructive">
            <Terminal className="h-4 w-4" />
            <AlertDescription>{validationErrors.contact_fields}</AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="reporter_name">Reporter Name</Label>
            <Input
              id="reporter_name"
              name="reporter_name"
              value={formData.reporter_name}
              onChange={handleInputChange}
              required
              disabled={isSubmitting}
              className={validationErrors.reporter_name ? 'border-red-500' : ''}
            />
            {validationErrors.reporter_name && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.reporter_name}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="reporter_relationship">Relationship to Incident</Label>
            <Select
              value={formData.reporter_relationship || undefined}
              onValueChange={value => handleSelectChange('reporter_relationship', value)}
              disabled={isSubmitting}
            >
              <SelectTrigger
                className={validationErrors.reporter_relationship ? 'border-red-500' : ''}
              >
                <SelectValue placeholder="Select relationship (optional)" />
              </SelectTrigger>
              <SelectContent>
                {Object.values(ReporterRelationship).map(relationship => (
                  <SelectItem key={relationship} value={relationship}>
                    {relationship.replace(/_/g, ' ')}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.reporter_relationship && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.reporter_relationship}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="reporter_phone">Phone Number</Label>
            <Input
              id="reporter_phone"
              name="reporter_phone"
              value={formData.reporter_phone}
              onChange={handleInputChange}
              placeholder="************"
              disabled={isSubmitting}
              className={validationErrors.reporter_phone ? 'border-red-500' : ''}
            />
            {validationErrors.reporter_phone && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.reporter_phone}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="reporter_email">Email Address</Label>
            <Input
              id="reporter_email"
              name="reporter_email"
              type="email"
              value={formData.reporter_email}
              onChange={handleInputChange}
              placeholder="<EMAIL>"
              disabled={isSubmitting}
              className={validationErrors.reporter_email ? 'border-red-500' : ''}
            />
            {validationErrors.reporter_email && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.reporter_email}</p>
            )}
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Incident Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="incident_date">Incident Date</Label>
            <Input
              id="incident_date"
              name="incident_date"
              type="date"
              value={formData.incident_date}
              onChange={handleInputChange}
              required
              disabled={isSubmitting}
              className={validationErrors.incident_date ? 'border-red-500' : ''}
              max={new Date().toISOString().split('T')[0]} // Prevent future dates in date picker
            />
            {validationErrors.incident_date && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.incident_date}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="incident_time">Incident Time</Label>
            <div className="relative">
              <Clock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                id="incident_time"
                name="incident_time"
                value={formData.incident_time}
                onChange={handleInputChange}
                placeholder="2:30 PM"
                disabled={isSubmitting}
                className={cn('pl-10', validationErrors.incident_time ? 'border-red-500' : '')}
              />
            </div>
            {validationErrors.incident_time && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.incident_time}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="incident_state">State Where Incident Occurred</Label>
            <Select
              value={formData.incident_state || undefined}
              onValueChange={value => handleSelectChange('incident_state', value as USState)}
              disabled={isSubmitting}
            >
              <SelectTrigger className={validationErrors.incident_state ? 'border-red-500' : ''}>
                <SelectValue placeholder="Select state" />
              </SelectTrigger>
              <SelectContent>
                {stateOptions.map(state => (
                  <SelectItem key={state} value={state}>
                    {state === 'UNKNOWN' ? 'Unknown' : state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors.incident_state && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.incident_state}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="incident_location">Incident Location</Label>
            <Input
              id="incident_location"
              name="incident_location"
              value={formData.incident_location}
              onChange={handleInputChange}
              placeholder="123 Main St, City, State (optional)"
              disabled={isSubmitting}
              className={validationErrors.incident_location ? 'border-red-500' : ''}
            />
            {validationErrors.incident_location && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.incident_location}</p>
            )}
          </div>

          <div className="space-y-2 md:col-span-2">
            <Label htmlFor="incident_description">Incident Description</Label>
            <Textarea
              id="incident_description"
              name="incident_description"
              value={formData.incident_description}
              onChange={handleInputChange}
              rows={4}
              required
              disabled={isSubmitting}
              className={validationErrors.incident_description ? 'border-red-500' : ''}
            />
            {validationErrors.incident_description && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.incident_description}</p>
            )}
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-medium">Policy Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="client_id">Client</Label>
            <div className="relative">
              {/* Simple Select Implementation */}
              <div
                className={cn(
                  'border rounded-md w-full',
                  validationErrors.client_id ? 'border-red-500' : 'border-input',
                  'focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2'
                )}
              >
                <button
                  type="button"
                  onClick={() => !isSubmitting && setOpenClientSelect(!openClientSelect)}
                  className="flex items-center justify-between w-full p-2 text-sm bg-background"
                  disabled={isSubmitting || isLoadingClients}
                >
                  <span>
                    {formData.client_id
                      ? clients.find(c => c.id === formData.client_id)?.name ||
                        'Select client'
                      : isLoadingClients
                        ? 'Loading...'
                        : 'Select client'}
                  </span>
                  <ChevronsUpDown className="h-4 w-4 opacity-50" />
                </button>

                {openClientSelect && (
                  <div className="absolute z-10 w-full mt-1 bg-popover border rounded-md shadow-md">
                    <div className="p-2 border-b">
                      <div className="flex items-center border rounded-md px-2">
                        <Search className="h-4 w-4 opacity-50" />
                        <input
                          value={clientSearch}
                          onChange={e => setClientSearch(e.target.value)}
                          placeholder="Search clients..."
                          className="flex h-8 w-full bg-transparent py-2 px-2 text-sm outline-none"
                          autoFocus
                        />
                      </div>
                    </div>
                    <div className="max-h-[200px] overflow-y-auto p-1">
                      {isLoadingClients ? (
                        <div className="p-2 text-sm text-center">Loading clients...</div>
                      ) : filteredClients.length === 0 ? (
                        <div className="p-2 text-sm text-center">No clients found</div>
                      ) : (
                        filteredClients.map(client => (
                          <div
                            key={client.id}
                            className={cn(
                              'flex items-center p-2 text-sm rounded-sm cursor-pointer',
                              formData.client_id === client.id
                                ? 'bg-accent text-accent-foreground'
                                : 'hover:bg-accent hover:text-accent-foreground'
                            )}
                            onClick={() => handleClientSelect(client.id)}
                          >
                            <Check
                              className={cn(
                                'mr-2 h-4 w-4',
                                formData.client_id === client.id ? 'opacity-100' : 'opacity-0'
                              )}
                            />
                            {client.name}
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                )}
              </div>
              {validationErrors.client_id && (
                <p className="text-sm text-red-500 mt-1">{validationErrors.client_id}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="policy_number">Policy Number</Label>
            <Input
              id="policy_number"
              name="policy_number"
              value={formData.policy_number || ''}
              onChange={handleInputChange}
              placeholder="Enter policy number (optional)"
              disabled={isSubmitting}
              className={validationErrors.policy_number ? 'border-red-500' : ''}
            />
            {validationErrors.policy_number && (
              <p className="text-sm text-red-500 mt-1">{validationErrors.policy_number}</p>
            )}
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button type="submit" disabled={isSubmitting || isLoadingClients}>
          {isSubmitting ? 'Submitting...' : 'Create FNOL'}
        </Button>
      </div>
    </form>
  )
}
