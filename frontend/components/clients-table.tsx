'use client'

import { useState, useEffect } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  ChevronRight,
  Loader2,
  AlertCircle,
  ChevronLeft,
  ChevronFirst,
  ChevronLast,
} from 'lucide-react'
import Link from 'next/link'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { ClientResponse, PaginatedResponse } from '@/lib/api/types'

export function ClientsTable() {
  const { formatDateShort } = useDateFormatter()
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [totalPages, setTotalPages] = useState(1)

  // Fetch client data using the useApi hook
  const fetchClients = async () => {
    console.log('[ClientsTable] Fetching clients...', { page, pageSize })
    const result = await api.clients.getClients({
      page: page - 1,
      size: pageSize,
      sort_by: 'created_at',
      sort_order: 'desc',
    })
    console.log('[ClientsTable] Raw API result:', result)

    // Update total pages state from the response
    if (result && result.pages) {
      setTotalPages(result.pages)
    }

    return result
  }

  const {
    data: clientsData,
    isLoading,
    error,
    refetch,
  } = useApi<PaginatedResponse<ClientResponse>>(fetchClients, [page, pageSize])

  // Refetch when page or pageSize changes
  useEffect(() => {
    refetch()
  }, [page, pageSize, refetch])

  const clients = clientsData?.items || []

  const getStatusColor = (isActive: boolean) => {
    return isActive
      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
  }

  const getInitials = (name: string): string => {
    const nameParts = name.split(' ')
    if (nameParts.length > 1) {
      return (nameParts[0][0] + nameParts[nameParts.length - 1][0]).toUpperCase()
    } else if (nameParts[0].length > 1) {
      return (nameParts[0][0] + nameParts[0][1]).toUpperCase()
    } else if (nameParts[0].length === 1) {
      return nameParts[0][0].toUpperCase()
    } else {
      return '??' // Default if name is empty or weird
    }
  }

  // Pagination controls
  const goToFirstPage = () => setPage(1)
  const goToPreviousPage = () => setPage(prevPage => Math.max(prevPage - 1, 1))
  const goToNextPage = () => setPage(prevPage => Math.min(prevPage + 1, totalPages))
  const goToLastPage = () => setPage(totalPages)

  // Loading State
  if (isLoading && !clientsData) {
    return (
      <div className="flex justify-center items-center p-10 border rounded-md">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading clients...</span>
      </div>
    )
  }

  // Error State
  if (error) {
    return (
      <div className="flex flex-col justify-center items-center p-10 border rounded-md text-destructive">
        <AlertCircle className="h-8 w-8 mb-2" />
        <p className="mb-4">Error loading clients: {String(error)}</p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    )
  }

  // Empty State
  if (!clients || clients.length === 0) {
    return (
      <div className="flex justify-center items-center p-10 border rounded-md">
        <p>No clients found.</p>
        {/* Optionally add a button to create the first client */}
      </div>
    )
  }

  return (
    <div className="flex flex-col rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Client</TableHead>
            <TableHead>Prefix</TableHead>
            {/* <TableHead>Type</TableHead> // Removed - Data not available in basic response */}
            <TableHead>Status</TableHead>
            {/* <TableHead>Claims</TableHead> // Removed - Data not available in basic response */}
            {/* <TableHead>FNOLs</TableHead> // Removed - Data not available in basic response */}
            <TableHead>Created</TableHead>
            <TableHead className="w-10"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {clients.map(client => (
            <TableRow key={client.id}>
              <TableCell>
                <div className="flex items-center gap-3">
                  <Avatar className="h-8 w-8">
                    {/* Placeholder image/fallback logic */}
                    <AvatarImage src={`/placeholder-user.jpg`} alt={client.name} />
                    <AvatarFallback>{getInitials(client.name)}</AvatarFallback>
                  </Avatar>
                  <Link
                    href={`/clients/${client.id}`}
                    className="font-medium text-primary hover:underline"
                  >
                    {client.name}
                  </Link>
                </div>
              </TableCell>
              <TableCell>{client.prefix}</TableCell>
              {/* <TableCell>
                <Badge variant="outline" className={getTypeColor(customer.type)}>
                  {customer.type.replace(/_/g, ' ')}
                </Badge>
              </TableCell> */}
              <TableCell>
                <Badge variant="outline" className={getStatusColor(client.active)}>
                  {client.active ? 'Active' : 'Inactive'}
                </Badge>
              </TableCell>
              {/* <TableCell>
                <Link href={`/clients/${client.id}/claims`} className="hover:underline">
                  {client.claimsCount}
                </Link>
              </TableCell>
              <TableCell>
                <Link href={`/clients/${client.id}/fnols`} className="hover:underline">
                  {client.fnolCount}
                </Link>
              </TableCell> */}
              <TableCell>{formatDateShort(client.created_at)}</TableCell>
              <TableCell>
                <Link href={`/clients/${client.id}`}>
                  <Button size="icon" variant="ghost" className="h-8 w-8">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between px-4 py-4 border-t">
        <div className="text-sm text-muted-foreground">
          Showing {clients.length} of {clientsData?.total || 0} clients • Page {page} of{' '}
          {totalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={goToFirstPage}
            disabled={page === 1 || isLoading}
          >
            <ChevronFirst className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={goToPreviousPage}
            disabled={page === 1 || isLoading}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={goToNextPage}
            disabled={page === totalPages || isLoading}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8"
            onClick={goToLastPage}
            disabled={page === totalPages || isLoading}
          >
            <ChevronLast className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
