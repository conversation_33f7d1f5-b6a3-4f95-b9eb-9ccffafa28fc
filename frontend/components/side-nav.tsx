'use client'

import type React from 'react'

import { Button } from '@/components/ui/button'
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { cn } from '@/lib/utils'
import {
  Calendar,
  ClipboardList,
  FileText,
  Home,
  Mail,
  PieChart,
  Settings,
  Users,
  Menu,
  X,
  AlertTriangle,
  DollarSign,
  Shield,
  LogOut,
} from 'lucide-react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { useAuth } from '@/components/auth-provider'

interface NavItemProps {
  href: string
  icon: React.ComponentType<{ className?: string }>
  title: string
  isCollapsed: boolean
  onClick?: (e: React.MouseEvent<HTMLButtonElement>) => void
}

function NavItem({ href, icon: Icon, title, isCollapsed, onClick }: NavItemProps) {
  const isActive = false // Would be determined by path in a real app

  if (isCollapsed) {
    return (
      <TooltipProvider>
        <Tooltip delayDuration={0}>
          <TooltipTrigger asChild>
            {onClick ? (
              <button onClick={onClick} className="flex items-center justify-center w-full">
                <div
                  className={cn(
                    'flex h-10 w-10 items-center justify-center rounded-md text-foreground',
                    isActive && 'bg-accent text-accent-foreground'
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <span className="sr-only">{title}</span>
                </div>
              </button>
            ) : (
              <Link href={href} className="flex items-center justify-center">
                <div
                  className={cn(
                    'flex h-10 w-10 items-center justify-center rounded-md text-foreground',
                    isActive && 'bg-accent text-accent-foreground'
                  )}
                >
                  <Icon className="h-5 w-5" />
                  <span className="sr-only">{title}</span>
                </div>
              </Link>
            )}
          </TooltipTrigger>
          <TooltipContent side="right" className="ml-1">
            {title}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )
  }

  return onClick ? (
    <button onClick={onClick} className="flex items-center w-full">
      <div
        className={cn(
          'flex w-full items-center rounded-md px-3 py-2 text-foreground hover:text-primary',
          isActive && 'bg-accent text-accent-foreground'
        )}
      >
        <Icon className="h-5 w-5 mr-2" />
        {title}
      </div>
    </button>
  ) : (
    <Link href={href} className="flex items-center w-full">
      <div
        className={cn(
          'flex w-full items-center rounded-md px-3 py-2 text-foreground hover:text-primary',
          isActive && 'bg-accent text-accent-foreground'
        )}
      >
        <Icon className="h-5 w-5 mr-2" />
        {title}
      </div>
    </Link>
  )
}

export default function SideNav() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const router = useRouter()
  const { logout } = useAuth()

  const handleLogout = async () => {
    await logout()
  }

  return (
    <div
      className={cn(
        'border-r bg-background h-[calc(100vh-4rem)] p-2 transition-all duration-300',
        isCollapsed ? 'w-16' : 'w-64'
      )}
    >
      <div className="flex flex-col h-full">
        <div className="flex justify-end p-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="text-foreground hover:text-primary"
          >
            {isCollapsed ? <Menu className="h-5 w-5" /> : <X className="h-5 w-5" />}
          </Button>
        </div>

        <div className="flex-1 py-4 space-y-1">
          <NavItem href="/" icon={Home} title="Dashboard" isCollapsed={isCollapsed} />
          <NavItem href="/claims" icon={ClipboardList} title="Claims" isCollapsed={isCollapsed} />
          <NavItem href="/fnol" icon={AlertTriangle} title="FNOL" isCollapsed={isCollapsed} />
          <NavItem href="/documents" icon={FileText} title="Documents" isCollapsed={isCollapsed} />
          <NavItem href="/tasks" icon={Calendar} title="Tasks" isCollapsed={isCollapsed} />
          <NavItem href="/clients" icon={Users} title="Clients" isCollapsed={isCollapsed} />
          <NavItem
            href="/financials"
            icon={DollarSign}
            title="Financials"
            isCollapsed={isCollapsed}
          />
          <NavItem href="/reports" icon={PieChart} title="Reports" isCollapsed={isCollapsed} />
        </div>

        <div className="pt-2 border-t space-y-1">
          <NavItem href="/settings" icon={Settings} title="Settings" isCollapsed={isCollapsed} />
          <div className="opacity-50 cursor-not-allowed">
            <NavItem
              href="#"
              icon={Shield}
              title="Admin"
              isCollapsed={isCollapsed}
              onClick={(e: React.MouseEvent<HTMLButtonElement>) => e.preventDefault()}
            />
          </div>
          <NavItem
            href="#"
            icon={LogOut}
            title="Logout"
            isCollapsed={isCollapsed}
            onClick={handleLogout}
          />
        </div>
      </div>
    </div>
  )
}
