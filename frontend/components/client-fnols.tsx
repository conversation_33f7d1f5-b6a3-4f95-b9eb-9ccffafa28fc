'use client'

import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { ChevronRight, ArrowRightLeft, PlusIcon } from 'lucide-react'
import Link from 'next/link'
import { useDateFormatter } from '@/hooks/useDateFormatter'

interface ClientFnolsProps {
  clientId: string
}

export function ClientFnols({ clientId }: ClientFnolsProps) {
  const { formatDateShort, formatRelativeDate } = useDateFormatter()

  // This would fetch FNOL data from the API filtered by customer ID
  // For now, we'll use mock data
  const fnols = [
    {
      id: '1',
      fnol_number: 'ACME-FNOL-0000001',
      reported_by: '<PERSON>',
      description: 'Single-vehicle accident on highway I-95',
      incident_date: new Date('2023-03-20'),
      reported_at: new Date('2023-03-21T09:30:00'),
      reporter_relationship: 'INSURED',
      communication_preference: 'EMAIL',
      has_claim: false,
    },
    {
      id: '2',
      fnol_number: 'ACME-FNOL-0000002',
      reported_by: 'Maria Garcia',
      description: 'Water damage to kitchen from broken pipe',
      incident_date: new Date('2023-03-18'),
      reported_at: new Date('2023-03-19T14:15:00'),
      reporter_relationship: 'INSURED',
      communication_preference: 'PHONE',
      has_claim: true,
      claim_id: '2',
      claim_number: 'ACME-2023-0000125',
    },
    {
      id: '3',
      fnol_number: 'ACME-FNOL-0000003',
      reported_by: 'Robert Johnson',
      description: 'Slip and fall incident at business location',
      incident_date: new Date('2023-03-15'),
      reported_at: new Date('2023-03-16T11:45:00'),
      reporter_relationship: 'INSURED',
      communication_preference: 'EMAIL',
      has_claim: true,
      claim_id: '3',
      claim_number: 'ACME-2023-0000119',
    },
    {
      id: '4',
      fnol_number: 'ACME-FNOL-0000004',
      reported_by: 'Emily Chen',
      description: 'Vehicle collision at intersection',
      incident_date: new Date('2023-03-10'),
      reported_at: new Date('2023-03-10T16:20:00'),
      reporter_relationship: 'CLAIMANT',
      communication_preference: 'EMAIL',
      has_claim: true,
      claim_id: '4',
      claim_number: 'ACME-2023-0000114',
    },
    {
      id: '5',
      fnol_number: 'ACME-FNOL-0000005',
      reported_by: 'David Williams',
      description: 'Hail damage to roof',
      incident_date: new Date('2023-03-05'),
      reported_at: new Date('2023-03-06T10:30:00'),
      reporter_relationship: 'AGENT',
      communication_preference: 'PHONE',
      has_claim: false,
    },
  ]

  const getRelationshipColor = (relationship: string) => {
    switch (relationship) {
      case 'INSURED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'CLAIMANT':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'AGENT':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'THIRD_PARTY':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>First Notice of Loss</CardTitle>
        <Link href="/fnol/new">
          <Button className="flex items-center gap-1">
            <PlusIcon className="h-4 w-4" />
            New FNOL
          </Button>
        </Link>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>FNOL #</TableHead>
                <TableHead>Reported By</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Incident Date</TableHead>
                <TableHead>Reported</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {fnols.map(fnol => (
                <TableRow key={fnol.id}>
                  <TableCell className="font-medium">
                    <Link href={`/fnol/${fnol.id}`} className="hover:underline">
                      {fnol.fnol_number}
                    </Link>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div>{fnol.reported_by}</div>
                      <Badge
                        variant="outline"
                        className={getRelationshipColor(fnol.reporter_relationship)}
                      >
                        {fnol.reporter_relationship}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>{fnol.description}</TableCell>
                  <TableCell>{formatDateShort(fnol.incident_date.toISOString())}</TableCell>
                  <TableCell>{formatRelativeDate(fnol.reported_at.toISOString())}</TableCell>
                  <TableCell>
                    {fnol.has_claim ? (
                      <div className="flex flex-col">
                        <Badge
                          variant="outline"
                          className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300"
                        >
                          Converted to Claim
                        </Badge>
                        <Link
                          href={`/claims/${fnol.claim_id}`}
                          className="text-xs text-primary hover:underline mt-1"
                        >
                          {fnol.claim_number}
                        </Link>
                      </div>
                    ) : (
                      <Badge
                        variant="outline"
                        className="bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300"
                      >
                        Pending Conversion
                      </Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      {!fnol.has_claim && (
                        <Button
                          size="icon"
                          variant="ghost"
                          className="h-8 w-8"
                          title="Convert to Claim"
                        >
                          <ArrowRightLeft className="h-4 w-4" />
                        </Button>
                      )}
                      <Link href={`/fnol/${fnol.id}`}>
                        <Button size="icon" variant="ghost" className="h-8 w-8" title="Details">
                          <ChevronRight className="h-4 w-4" />
                        </Button>
                      </Link>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  )
}
