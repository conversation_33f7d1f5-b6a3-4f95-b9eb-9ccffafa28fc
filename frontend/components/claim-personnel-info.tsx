'use client'

import { User } from 'lucide-react'
import Link from 'next/link'
import { Loading } from '@/components/ui/loading'
import { EmptyState } from '@/components/ui/empty-state'
import { Claim } from '@/lib/api/types'

interface ClaimPersonnelInfoProps {
  claimNumber: string
  claim: Claim | null
  isLoading: boolean
  error: string | null
}

export function ClaimPersonnelInfo({
  claimNumber,
  claim,
  isLoading,
  error,
}: ClaimPersonnelInfoProps) {
  if (isLoading) {
    return (
      <div className="bg-muted rounded-lg p-4">
        <Loading variant="spinner" size="sm" text="Loading personnel information..." centered />
      </div>
    )
  }

  if (error || !claim) {
    return (
      <div className="bg-muted rounded-lg p-4">
        <EmptyState
          variant="error"
          title="Could not load personnel"
          description={error || 'Failed to load personnel information'}
          actionLabel="Try Again"
          onAction={() => {}} // No longer needed since data comes from parent
          className="py-2"
        />
      </div>
    )
  }

  return (
    <div className="bg-muted rounded-lg p-4 flex flex-col gap-4">
      <div className="font-medium flex items-center gap-2">
        <User className="h-4 w-4 text-muted-foreground" />
        Personnel
      </div>
      <div className="grid grid-cols-1 gap-2 text-sm">
        <div className="flex justify-between">
          <span className="text-muted-foreground">Adjuster</span>
          {claim.assigned_to ? (
            <span className="font-medium">
              {claim.assigned_to.first_name} {claim.assigned_to.last_name}
            </span>
          ) : claim.assigned_to_id ? (
            <span className="text-muted-foreground font-mono text-xs">{claim.assigned_to_id}</span>
          ) : (
            <span className="text-muted-foreground italic">Unassigned</span>
          )}
        </div>
        {(claim.supervisor || claim.supervisor_id) && (
          <div className="flex justify-between">
            <span className="text-muted-foreground">Supervisor</span>
            {claim.supervisor ? (
              <span className="font-medium">
                {claim.supervisor.first_name} {claim.supervisor.last_name}
              </span>
            ) : (
              <span className="text-muted-foreground font-mono text-xs">{claim.supervisor_id}</span>
            )}
          </div>
        )}
        <div className="flex justify-between">
          <span className="text-muted-foreground">Client</span>
          {claim.client && (
            <Link href={`/clients/${claim.client.id}`} className="hover:underline">
              {claim.client.name}
            </Link>
          )}
        </div>
      </div>
    </div>
  )
}
