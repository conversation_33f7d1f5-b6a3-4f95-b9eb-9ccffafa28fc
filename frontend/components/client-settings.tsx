import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ClientSettingsProps {
  clientId: string
}

export function ClientSettings({ clientId }: ClientSettingsProps) {
  // This would fetch client settings from the API
  // For now, we'll use mock data
  const settings = {
    general: {
      name: 'Acme Insurance Co.',
      prefix: 'ACME',
      description: 'A leading provider of property and casualty insurance products.',
      active: true,
    },
    contact: {
      name: '<PERSON>',
      email: '<EMAIL>',
      phone: '(*************',
      street: '123 Insurance Way',
      city: 'New York',
      state: 'NY',
      zip: '10001',
      country: 'USA',
    },
    configuration: {
      claim_numbering_format: '{PREFIX}-{YEAR}-{SEQUENCE:7}',
      fnol_numbering_format: '{PREFIX}-FNOL-{SEQUENCE:7}',
      reserve_approval_threshold: 25000,
      payment_approval_threshold: 10000,
      require_supervisor_approval: true,
      allow_direct_fnol_conversion: false,
    },
    integrations: {
      api_enabled: true,
      webhook_url: 'https://api.acmeinsurance.com/webhooks/claims',
      api_key: 'sk_acme_123456789',
      notification_email: '<EMAIL>',
    },
  }

  return (
    <Tabs defaultValue="general" className="w-full">
      <TabsList className="grid w-full md:w-auto grid-cols-4 md:flex">
        <TabsTrigger value="general">General</TabsTrigger>
        <TabsTrigger value="contact">Contact</TabsTrigger>
        <TabsTrigger value="configuration">Configuration</TabsTrigger>
        <TabsTrigger value="integrations">Integrations</TabsTrigger>
      </TabsList>

      <TabsContent value="general" className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>General Information</CardTitle>
            <CardDescription>Basic information about the client</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="client-name">Client Name</Label>
              <Input id="client-name" defaultValue={settings.general.name} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="client-prefix">Prefix</Label>
              <Input id="client-prefix" defaultValue={settings.general.prefix} />
              <p className="text-xs text-muted-foreground">
                This prefix is used in claim numbers and other identifiers.
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="client-description">Description</Label>
              <Textarea id="client-description" defaultValue={settings.general.description} />
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="client-active" defaultChecked={settings.general.active} />
              <Label htmlFor="client-active">Active</Label>
            </div>
            <Button>Save Changes</Button>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="contact" className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Contact Information</CardTitle>
            <CardDescription>Primary contact details for the customer</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="contact-name">Contact Name</Label>
              <Input id="contact-name" defaultValue={settings.contact.name} />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contact-email">Email</Label>
                <Input id="contact-email" type="email" defaultValue={settings.contact.email} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact-phone">Phone</Label>
                <Input id="contact-phone" defaultValue={settings.contact.phone} />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="contact-street">Street Address</Label>
              <Input id="contact-street" defaultValue={settings.contact.street} />
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contact-city">City</Label>
                <Input id="contact-city" defaultValue={settings.contact.city} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact-state">State</Label>
                <Input id="contact-state" defaultValue={settings.contact.state} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact-zip">ZIP Code</Label>
                <Input id="contact-zip" defaultValue={settings.contact.zip} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contact-country">Country</Label>
                <Input id="contact-country" defaultValue={settings.contact.country} />
              </div>
            </div>
            <Button>Save Changes</Button>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="configuration" className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>System Configuration</CardTitle>
            <CardDescription>Configure claim numbering and approval thresholds</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="claim-numbering">Claim Numbering Format</Label>
              <Input
                id="claim-numbering"
                defaultValue={settings.configuration.claim_numbering_format}
              />
              <p className="text-xs text-muted-foreground">
                Available placeholders: {'{PREFIX}'}, {'{YEAR}'}, {'{SEQUENCE:n}'}, {'{MONTH}'},{' '}
                {'{DAY}'}
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="fnol-numbering">FNOL Numbering Format</Label>
              <Input
                id="fnol-numbering"
                defaultValue={settings.configuration.fnol_numbering_format}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="reserve-threshold">Reserve Approval Threshold ($)</Label>
                <Input
                  id="reserve-threshold"
                  type="number"
                  defaultValue={settings.configuration.reserve_approval_threshold}
                />
                <p className="text-xs text-muted-foreground">
                  Reserves above this amount require supervisor approval
                </p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="payment-threshold">Payment Approval Threshold ($)</Label>
                <Input
                  id="payment-threshold"
                  type="number"
                  defaultValue={settings.configuration.payment_approval_threshold}
                />
                <p className="text-xs text-muted-foreground">
                  Payments above this amount require supervisor approval
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="supervisor-approval"
                defaultChecked={settings.configuration.require_supervisor_approval}
              />
              <Label htmlFor="supervisor-approval">
                Require Supervisor Approval for Claim Closure
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                id="direct-conversion"
                defaultChecked={settings.configuration.allow_direct_fnol_conversion}
              />
              <Label htmlFor="direct-conversion">Allow Direct FNOL to Claim Conversion</Label>
            </div>
            <Button>Save Changes</Button>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="integrations" className="mt-6 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>API & Integrations</CardTitle>
            <CardDescription>Configure API access and webhooks</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch id="api-enabled" defaultChecked={settings.integrations.api_enabled} />
              <Label htmlFor="api-enabled">Enable API Access</Label>
            </div>
            <div className="space-y-2">
              <Label htmlFor="webhook-url">Webhook URL</Label>
              <Input id="webhook-url" defaultValue={settings.integrations.webhook_url} />
              <p className="text-xs text-muted-foreground">
                Notifications about claim updates will be sent to this URL
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="api-key">API Key</Label>
              <div className="flex gap-2">
                <Input id="api-key" type="password" defaultValue={settings.integrations.api_key} />
                <Button variant="outline">Regenerate</Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="notification-email">Notification Email</Label>
              <Input
                id="notification-email"
                defaultValue={settings.integrations.notification_email}
              />
              <p className="text-xs text-muted-foreground">
                System notifications will be sent to this email address
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="notification-events">Notification Events</Label>
              <Select defaultValue="all">
                <SelectTrigger id="notification-events">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Events</SelectItem>
                  <SelectItem value="claims">Claims Only</SelectItem>
                  <SelectItem value="fnol">FNOL Only</SelectItem>
                  <SelectItem value="payments">Payments Only</SelectItem>
                  <SelectItem value="custom">Custom</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button>Save Changes</Button>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  )
}
