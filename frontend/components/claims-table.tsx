'use client'

import { useState, useMemo } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination'
import { ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react'
import Link from 'next/link'
import { SortableHeader, type SortDirection } from '@/components/ui/sortable-header'
import { cn } from '@/lib/utils'
import { Card, CardContent } from '@/components/ui/card'
import { useApi } from '@/hooks/useApi'
import { api } from '../lib/api'
import {
  Claim,
  ClientResponse,
  ClaimQueryParams,
  PaginatedResponse,
  ClaimStatus,
  ClaimType,
  UserResponse,
} from '@/lib/api/types'

interface ClaimsTableProps {
  search?: string
  status?: ClaimStatus
  type?: ClaimType
}

export function ClaimsTable({ search, status, type }: ClaimsTableProps) {
  const [sortColumn, setSortColumn] = useState<string | null>('date')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(25)

  // Helper function to display assignee names properly
  const getAssigneeDisplayName = (assignedTo?: UserResponse | null): string => {
    if (!assignedTo) return 'Unassigned'
    const fullName = `${assignedTo.first_name} ${assignedTo.last_name}`.trim()
    return fullName || assignedTo.email || 'Unknown User'
  }

  // Fetch claims data from API
  const {
    data: claimsData,
    isLoading,
    error,
  } = useApi(
    () =>
      api.claims.getClaims({
        page: currentPage, // Frontend uses 1-based pagination, API client handles conversion to skip/limit
        size: pageSize,
        search,
        status,
        type,
        // Use type assertion to include 'include' parameter which exists in API but isn't in the type definition
        include: ['client', 'assigned_to'], // Request to include client and assigned_to data
      } as ClaimQueryParams & { include: string[] }),
    [currentPage, pageSize, search, status, type]
  )

  // Transform API data to match expected format
  const { claims, totalItems, totalPages } = useMemo(() => {
    // Handle either a direct array or a paginated structure with 'items' property
    const claimsArray = Array.isArray(claimsData) ? claimsData : claimsData?.items || []

    // Since backend returns just an array, we need to estimate total items
    // If we get a full page (pageSize items), there might be more pages
    // If we get less than pageSize items, this is likely the last page
    const currentPageItems = claimsArray.length

    let estimatedTotal: number
    let estimatedPages: number

    if (Array.isArray(claimsData)) {
      // Backend returned a simple array (current behavior)
      if (currentPageItems === pageSize) {
        // Full page - there are likely more pages
        // Since we can't know the exact total, we'll show "at least one more page"
        estimatedTotal = currentPage * pageSize + 1 // Assume at least one more item
        estimatedPages = currentPage + 1 // Show at least one more page
      } else if (currentPageItems < pageSize) {
        // This page is not full, so it's the last page
        estimatedTotal = (currentPage - 1) * pageSize + currentPageItems
        estimatedPages = currentPage
      } else {
        // Fallback - shouldn't happen but just in case
        estimatedTotal = currentPage * pageSize
        estimatedPages = currentPage
      }
    } else {
      // Backend returned a paginated structure (future behavior)
      estimatedTotal = claimsData?.total || currentPageItems
      estimatedPages = claimsData?.pages || Math.ceil(estimatedTotal / pageSize)
    }

    // Add extra validation to ensure we're working with valid data
    const processedClaims = claimsArray
      .filter(claim => claim && typeof claim === 'object') // Only process valid claim objects
      .map(claim => {
        try {
          // The API will include client data but TypeScript doesn't know about it
          // Use type assertion to handle this
          const claimWithClient = claim as Claim & { client?: { name: string; prefix: string } }

          // Handle case where total_amount might be missing or null
          const totalAmount = claim.total_amount ?? 0

          // Ensure all required fields exist with fallbacks
          return {
            id: claim.id || 'unknown-id',
            number: claim.claim_number || 'No Number',
            policy_number: claim.policy_number || null,
            claimant: claim.claimant_name || 'Unknown Claimant',
            type: claim.type || 'UNKNOWN',
            status: claim.status || 'UNKNOWN',
            date: (() => {
              const dateObj = new Date(claim.date_of_loss || new Date())
              // Use a consistent format that works on both server and client
              return dateObj.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                timeZone: 'UTC', // Force UTC to avoid timezone differences
              })
            })(),
            dateObj: new Date(claim.date_of_loss || new Date()),
            amount: `$${totalAmount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
            amountValue: totalAmount,
            assignee: getAssigneeDisplayName(claim.assigned_to),
            client: {
              id: claim.client_id || 'unknown-client',
              name: claimWithClient.client?.name || 'Unknown Client',
              prefix: claimWithClient.client?.prefix || '',
            },
          }
        } catch (err) {
          console.error('Error processing claim data:', err, claim)
          // Return a fallback claim object if processing fails
          return {
            id: 'error-' + Math.random().toString(36).slice(2),
            number: 'Error',
            policy_number: null,
            claimant: 'Error processing claim',
            type: 'UNKNOWN',
            status: 'UNKNOWN',
            date: new Date().toLocaleDateString('en-US', {
              month: 'short',
              day: 'numeric',
              year: 'numeric',
              timeZone: 'UTC',
            }),
            dateObj: new Date(),
            amount: '$0.00',
            amountValue: 0,
            assignee: 'Unassigned',
            client: {
              id: 'unknown',
              name: 'Unknown Client',
              prefix: '',
            },
          }
        }
      })

    return {
      claims: processedClaims,
      totalItems: estimatedTotal,
      totalPages: estimatedPages,
    }
  }, [claimsData, currentPage, pageSize, getAssigneeDisplayName])

  const handleSort = (column: string) => {
    if (sortColumn === column) {
      // Toggle direction if same column
      setSortDirection(sortDirection === 'asc' ? 'desc' : sortDirection === 'desc' ? null : 'asc')
      if (sortDirection === null) {
        setSortColumn(null)
      }
    } else {
      // Set new column and default to ascending
      setSortColumn(column)
      setSortDirection('asc')
    }
  }

  const sortedClaims = useMemo(() => {
    if (!claims.length) return []

    return [...claims].sort((a, b) => {
      if (!sortColumn || sortDirection === null) return 0

      const direction = sortDirection === 'asc' ? 1 : -1

      switch (sortColumn) {
        case 'number':
          return direction * a.number.localeCompare(b.number)
        case 'policy_number':
          return direction * (a.policy_number || '').localeCompare(b.policy_number || '')
        case 'client':
          return direction * a.client.name.localeCompare(b.client.name)
        case 'claimant':
          return direction * a.claimant.localeCompare(b.claimant)
        case 'type':
          return direction * a.type.localeCompare(b.type)
        case 'status':
          return direction * a.status.localeCompare(b.status)
        case 'date':
          return direction * (a.dateObj.getTime() - b.dateObj.getTime())
        case 'assignee':
          return direction * a.assignee.localeCompare(b.assignee)
        case 'amount':
          return direction * (a.amountValue - b.amountValue)
        default:
          return 0
      }
    })
  }, [claims, sortColumn, sortDirection])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'INVESTIGATION':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'SETTLEMENT':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'LITIGATION':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'RECOVERY':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'CLOSED_SETTLED':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
      case 'CLOSED_DENIED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'AUTO':
        return 'bg-sky-100 text-sky-800 dark:bg-sky-900 dark:text-sky-300'
      case 'PROPERTY':
        return 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900 dark:text-emerald-300'
      case 'GENERAL_LIABILITY':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-center items-center h-40">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-center items-center h-40 text-red-500">
            Error loading claims: {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  // No data state
  if (!sortedClaims.length) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-center items-center h-40 text-muted-foreground">
            No claims found
          </div>
        </CardContent>
      </Card>
    )
  }

  // Pagination handlers
  const handlePageChange = (page: number) => {
    // Ensure page is always at least 1
    setCurrentPage(Math.max(1, page))
  }

  const handlePageSizeChange = (newPageSize: string) => {
    setPageSize(parseInt(newPageSize))
    setCurrentPage(1) // Reset to first page when changing page size
  }

  return (
    <div className="space-y-4">
      {/* Page size selector */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <span className="text-sm text-muted-foreground">Show</span>
          <Select value={pageSize.toString()} onValueChange={handlePageSizeChange}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-muted-foreground">claims per page</span>
        </div>
        <div className="text-sm text-muted-foreground">
          Showing {(currentPage - 1) * pageSize + 1} to{' '}
          {Math.min(currentPage * pageSize, totalItems)} of {totalItems} claims
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <SortableHeader
                column="number"
                label="Claim #"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="policy_number"
                label="Policy #"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="client"
                label="Client"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="claimant"
                label="Claimant"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="type"
                label="Type"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="status"
                label="Status"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="date"
                label="Date"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="assignee"
                label="Assignee"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
              />
              <SortableHeader
                column="amount"
                label="Amount"
                sortColumn={sortColumn}
                sortDirection={sortDirection}
                onSort={handleSort}
                className="text-right"
              />
              <TableHead className="w-10"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedClaims.map(claim => (
              <TableRow key={claim.id}>
                <TableCell className="font-medium">
                  <Link
                    href={`/claims/${claim.number}`}
                    className="hover:text-primary hover:underline"
                  >
                    {claim.number}
                  </Link>
                </TableCell>
                <TableCell>{claim.policy_number || '-'}</TableCell>
                <TableCell>{claim.client.name}</TableCell>
                <TableCell>{claim.claimant}</TableCell>
                <TableCell>
                  <Badge variant="outline" className={getTypeColor(claim.type)}>
                    {claim.type.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className={getStatusColor(claim.status)}>
                    {claim.status.replace('_', ' ')}
                  </Badge>
                </TableCell>
                <TableCell>{claim.date}</TableCell>
                <TableCell>{claim.assignee}</TableCell>
                <TableCell className="text-right">{claim.amount}</TableCell>
                <TableCell>
                  <Link href={`/claims/${claim.number}`}>
                    <Button size="icon" variant="ghost" className="h-8 w-8">
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </Link>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center space-x-2">
          <Pagination>
            <PaginationContent>
              {/* First Page Button */}
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={e => {
                    e.preventDefault()
                    if (currentPage > 1) handlePageChange(1)
                  }}
                  className={cn(
                    'gap-1 pl-2.5',
                    currentPage <= 1 ? 'pointer-events-none opacity-50' : ''
                  )}
                  aria-label="Go to first page"
                >
                  <ChevronsLeft className="h-4 w-4" />
                  <span>First</span>
                </PaginationLink>
              </PaginationItem>

              {/* Previous Page Button */}
              <PaginationItem>
                <PaginationPrevious
                  href="#"
                  onClick={e => {
                    e.preventDefault()
                    if (currentPage > 1) handlePageChange(currentPage - 1)
                  }}
                  className={currentPage <= 1 ? 'pointer-events-none opacity-50' : ''}
                />
              </PaginationItem>

              {/* Page numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum: number
                if (totalPages <= 5) {
                  pageNum = i + 1
                } else if (currentPage <= 3) {
                  pageNum = i + 1
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i
                } else {
                  pageNum = currentPage - 2 + i
                }

                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      href="#"
                      onClick={e => {
                        e.preventDefault()
                        handlePageChange(pageNum)
                      }}
                      isActive={currentPage === pageNum}
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                )
              })}

              {/* Next Page Button */}
              <PaginationItem>
                <PaginationNext
                  href="#"
                  onClick={e => {
                    e.preventDefault()
                    if (currentPage < totalPages) handlePageChange(currentPage + 1)
                  }}
                  className={currentPage >= totalPages ? 'pointer-events-none opacity-50' : ''}
                />
              </PaginationItem>

              {/* Last Page Button */}
              <PaginationItem>
                <PaginationLink
                  href="#"
                  onClick={e => {
                    e.preventDefault()
                    if (currentPage < totalPages) handlePageChange(totalPages)
                  }}
                  className={cn(
                    'gap-1 pr-2.5',
                    currentPage >= totalPages ? 'pointer-events-none opacity-50' : ''
                  )}
                  aria-label="Go to last page"
                >
                  <span>Last</span>
                  <ChevronsRight className="h-4 w-4" />
                </PaginationLink>
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  )
}
