'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  ArrowLeft,
  Calendar,
  FileText,
  User,
  ArrowRightLeft,
  MapPin,
  Clock,
  Phone,
} from 'lucide-react'
import Link from 'next/link'
import { FnolAssociatedClaims } from '@/components/fnol-associated-claims'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { FNOLResponse, FNOLStatus, CommunicationPreference } from '@/lib/api/types'
import { Loading } from '@/components/ui/loading'
import { EmptyState } from '@/components/ui/empty-state'
import { useDateFormatter } from '@/hooks/useDateFormatter'
import { useParams } from 'next/navigation'
import FnolEditModal from '@/components/fnol-edit-modal'
import FnolConvertModal from '@/components/fnol-convert-modal'

export default function FnolDetailPage() {
  const params = useParams<{ id: string }>()
  const fnolId = params.id
  const { formatDate, formatDateTime } = useDateFormatter()

  const [showEditModal, setShowEditModal] = useState(false)
  const [showConvertModal, setShowConvertModal] = useState(false)

  const {
    data: fnol,
    isLoading,
    error,
    refetch,
  } = useApi<FNOLResponse>(() => api.fnol.getFNOLById(fnolId), [fnolId])

  const getRelationshipColor = (relationship: string | undefined) => {
    if (!relationship) return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    switch (relationship) {
      case 'INSURED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'CLAIMANT':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'AGENT':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
      case 'THIRD_PARTY':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'FAMILY_MEMBER':
        return 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300'
      case 'LEGAL_REPRESENTATIVE':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300'
      case 'OTHER':
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300'
    }
  }

  const getCommunicationIcon = (preference: CommunicationPreference | undefined | null) => {
    if (!preference) return null

    switch (preference) {
      case CommunicationPreference.EMAIL:
        return (
          <svg
            className="h-4 w-4 text-muted-foreground"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <rect width="20" height="16" x="2" y="4" rx="2" />
            <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7" />
          </svg>
        )
      case CommunicationPreference.PHONE:
        return <Phone className="h-4 w-4 text-muted-foreground" />
      case CommunicationPreference.TEXT:
        return (
          <svg
            className="h-4 w-4 text-muted-foreground"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
          </svg>
        )
      case CommunicationPreference.MAIL:
        return (
          <svg
            className="h-4 w-4 text-muted-foreground"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M22 17a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V9.5C2 7 4 5 6.5 5H18c2.2 0 4 1.8 4 4v8Z" />
            <polyline points="15,9 18,9 18,11" />
            <path d="M6.5 5C9 5 11 7 11 9.5V17a2 2 0 0 1-2 2v0" />
            <line x1="6" x2="7" y1="10" y2="10" />
          </svg>
        )
      case CommunicationPreference.PORTAL:
        return (
          <svg
            className="h-4 w-4 text-muted-foreground"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <rect width="18" height="18" x="3" y="3" rx="2" ry="2" />
            <line x1="3" x2="21" y1="9" y2="9" />
            <line x1="9" x2="9" y1="21" y2="9" />
          </svg>
        )
      default:
        return null
    }
  }

  const handleEditSuccess = () => {
    refetch()
  }

  const handleConvertSuccess = () => {
    refetch()
  }

  // Convert 24-hour time from backend to 12-hour format for display
  const convertTo12Hour = (time24h: string): string => {
    if (!time24h || !time24h.includes(':')) return time24h

    const [hours, minutes] = time24h.split(':')
    const hour24 = parseInt(hours, 10)
    const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24
    const ampm = hour24 >= 12 ? 'PM' : 'AM'

    return `${hour12}:${minutes} ${ampm}`
  }

  if (isLoading) {
    return (
      <div className="flex justify-center p-6">
        <Loading size="lg" text="Loading FNOL details..." centered />
      </div>
    )
  }

  if (error || !fnol) {
    return (
      <EmptyState
        variant="error"
        title="Failed to load FNOL details"
        description={error || 'The requested FNOL could not be found.'}
        actionLabel="Try Again"
        onAction={refetch}
      />
    )
  }

  const claims = fnol.claims ?? []

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center gap-2">
        <Link href="/fnol">
          <Button variant="outline" size="icon" className="rounded-full h-8 w-8">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-2xl font-bold tracking-tight">FNOL Details</h1>
        <Badge variant="outline" className="ml-2">
          {fnol.fnol_number}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-xl">{fnol.reported_by}</CardTitle>
                <p className="text-sm text-muted-foreground mt-1">Person reporting the incident</p>
                <div className="flex items-center gap-2 mt-2">
                  {fnol.reporter_relationship && (
                    <Badge
                      variant="outline"
                      className={getRelationshipColor(fnol.reporter_relationship)}
                    >
                      {fnol.reporter_relationship}
                    </Badge>
                  )}
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setShowEditModal(true)}>
                  Edit
                </Button>
                {/* Only hide convert button for rejected FNOLs */}
                {fnol.status !== FNOLStatus.REJECTED && (
                  <Button
                    className="flex items-center gap-1"
                    onClick={() => setShowConvertModal(true)}
                  >
                    <ArrowRightLeft className="h-4 w-4 mr-1" />
                    Convert to Claim
                  </Button>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h3 className="font-medium mb-2">Description</h3>
                  <p className="text-sm text-muted-foreground">
                    {fnol.description || 'No description provided'}
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="font-medium mb-2">Incident Details</h3>
                    <dl className="grid grid-cols-1 gap-2 text-sm">
                      <div className="grid grid-cols-2">
                        <dt className="text-muted-foreground">Date</dt>
                        <dd>{fnol.incident_date ? formatDate(fnol.incident_date) : 'N/A'}</dd>
                      </div>
                      {fnol.incident_time && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Time</dt>
                          <dd>{convertTo12Hour(fnol.incident_time)}</dd>
                        </div>
                      )}
                      {fnol.incident_state && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">State</dt>
                          <dd>
                            {fnol.incident_state === 'UNKNOWN' ? 'Unknown' : fnol.incident_state}
                          </dd>
                        </div>
                      )}
                      {fnol.incident_location && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Location</dt>
                          <dd>{fnol.incident_location}</dd>
                        </div>
                      )}
                      {fnol.policy_number && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Policy Number</dt>
                          <dd>{fnol.policy_number}</dd>
                        </div>
                      )}
                    </dl>
                  </div>

                  <div>
                    <h3 className="font-medium mb-2">Contact Information</h3>
                    <dl className="grid grid-cols-1 gap-2 text-sm">
                      {fnol.reporter_phone && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Phone</dt>
                          <dd>{fnol.reporter_phone}</dd>
                        </div>
                      )}
                      {fnol.reporter_email && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Email</dt>
                          <dd>{fnol.reporter_email}</dd>
                        </div>
                      )}
                      {!fnol.reporter_phone && !fnol.reporter_email && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Contact</dt>
                          <dd>No contact information provided</dd>
                        </div>
                      )}
                      {fnol.communication_preference && (
                        <div className="grid grid-cols-2">
                          <dt className="text-muted-foreground">Preferred Contact Method</dt>
                          <dd className="flex items-center">
                            {getCommunicationIcon(fnol.communication_preference)}
                            <span className="ml-1">{fnol.communication_preference}</span>
                          </dd>
                        </div>
                      )}
                    </dl>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="mt-6">
            {/* Pass fetched claims data and FNOL status */}
            <FnolAssociatedClaims fnolId={fnol.id} claims={claims} fnolStatus={fnol.status} />
          </div>
        </div>

        <div className="space-y-6">
          <div className="bg-muted rounded-lg p-4 flex flex-col gap-4">
            <div className="font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              Key Dates
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm">
              <div className="text-muted-foreground">Reported</div>
              <div>{formatDateTime(fnol.reported_at || fnol.created_at)}</div>
              <div className="text-muted-foreground">Incident Date</div>
              <div>{fnol.incident_date ? formatDate(fnol.incident_date) : 'N/A'}</div>
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle className="text-md">Client Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                {fnol.client ? (
                  <>
                    <div className="font-medium">{fnol.client.name}</div>
                    <div className="text-muted-foreground">{fnol.client.description || ''}</div>
                    <div className="mt-2">
                      <Link
                        href={`/clients/${fnol.client_id}`}
                        className="text-primary hover:underline"
                      >
                        View Client Details
                      </Link>
                    </div>
                  </>
                ) : (
                  <div className="text-muted-foreground">No client information available</div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modal for editing FNOL */}
      {showEditModal && (
        <FnolEditModal
          isOpen={showEditModal}
          onClose={() => setShowEditModal(false)}
          fnol={fnol}
          onSuccess={handleEditSuccess}
        />
      )}

      {/* Modal for converting FNOL to claim */}
      {showConvertModal && (
        <FnolConvertModal
          isOpen={showConvertModal}
          onClose={() => setShowConvertModal(false)}
          fnolId={fnol.id}
          onSuccess={handleConvertSuccess}
          autoClose={true}
        />
      )}
    </div>
  )
}
