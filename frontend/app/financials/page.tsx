'use client'

import React, { useState, useMemo } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { FinancialsTable } from '@/components/financials-table'
import { ReservesTable } from '@/components/reserves-table'
import { Input } from '@/components/ui/input'
import { Search, AlertCircle } from 'lucide-react'
import { useApi } from '@/hooks/useApi'
import { api } from '@/lib/api'
import { Claim, PaginatedResponse, ClaimFinancialsInDB } from '@/lib/api/types'
import { Skeleton } from '@/components/ui/skeleton'

export default function FinancialsPage() {
  const {
    data: claimsData,
    isLoading,
    error,
  } = useApi<PaginatedResponse<Claim>>(
    () => api.claims.getClaims({ include: ['client', 'financials'], size: 100 }),
    []
  )

  const claims = useMemo(() => claimsData?.items || [], [claimsData])

  const summaries = React.useMemo(() => {
    if (!claims || claims.length === 0) {
      return { totalReserves: 0, totalPayments: 0, totalRecoveryReceived: 0 }
    }

    let totalReserves = 0
    let totalRecoveryReceived = 0

    claims.forEach(claim => {
      if (claim.financials) {
        claim.financials.reserves.forEach(reserve => {
          totalReserves += parseFloat(reserve.amount) || 0
        })
        totalRecoveryReceived += parseFloat(claim.financials.recovery_received || '0') || 0
      }
    })

    return { totalReserves, totalPayments: undefined, totalRecoveryReceived }
  }, [claims])

  const formatCurrency = (value: number | undefined) => {
    if (value === undefined) return 'N/A'
    return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value)
  }

  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Financials</h1>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Reserves</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-3/4" />
            ) : error ? (
              <span className="text-red-500 text-sm">Error</span>
            ) : (
              <div className="text-2xl font-bold">{formatCurrency(summaries.totalReserves)}</div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Payments</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-3/4" />
            ) : error ? (
              <span className="text-red-500 text-sm">Error</span>
            ) : (
              <div className="text-2xl font-bold text-muted-foreground">
                {formatCurrency(summaries.totalPayments)}
              </div>
            )}
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium">Total Recovery Received</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <Skeleton className="h-8 w-3/4" />
            ) : error ? (
              <span className="text-red-500 text-sm">Error</span>
            ) : (
              <div className="text-2xl font-bold">
                {formatCurrency(summaries.totalRecoveryReceived)}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <div className="flex w-full items-center space-x-2 mb-4">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search by claim number, claimant, or amount..."
            className="pl-8"
            disabled={isLoading || !!error}
          />
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="flex items-center gap-2 text-red-600 bg-red-100 p-3 rounded-md border border-red-300">
          <AlertCircle className="h-5 w-5" />
          {/* Safely access error message */}
          <span>
            Error fetching financial data:{' '}
            {typeof error === 'string' ? error : (error as any)?.message || 'Unknown error'}
          </span>
        </div>
      )}

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full md:w-auto grid-cols-3 md:flex">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="reserves">Reserves</TabsTrigger>
        </TabsList>

        {isLoading ? (
          <div className="mt-6">
            <Skeleton className="h-64 w-full" />
          </div>
        ) : (
          <>
            <TabsContent value="overview" className="mt-6">
              <FinancialsTable claims={claims} />
            </TabsContent>

            <TabsContent value="reserves" className="mt-6">
              <ReservesTable claims={claims} />
            </TabsContent>
          </>
        )}
      </Tabs>
    </div>
  )
}
