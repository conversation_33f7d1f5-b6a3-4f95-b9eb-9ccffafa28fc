import { NextRequest, NextResponse } from 'next/server'
import { handleGetRequest, handleDataRequest } from '@/lib/api-proxy-utils'
import { HTTP_STATUS } from '@/lib/constants'

/**
 * Handles GET requests to /api/v1/customers (deprecated - use /api/v1/clients)
 * Proxies the request to the backend API to list clients
 */
export async function GET(request: NextRequest) {
  return handleGetRequest(request, '/api/v1/clients', {
    errorMessage: 'Error fetching clients',
  })
}

/**
 * Handles POST requests to /api/v1/customers (deprecated - use /api/v1/clients)
 * Proxies the request to the backend API to create a client
 */
export async function POST(request: NextRequest) {
  return handleDataRequest(request, '/api/v1/clients', {
    method: 'POST',
    errorMessage: 'Error creating client',
  })
}

/**
 * Handles OPTIONS requests to /api/v1/customers
 * Returns allowed methods for the endpoint
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: HTTP_STATUS.NO_CONTENT,
    headers: {
      Allow: 'GET, POST, OPTIONS',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
