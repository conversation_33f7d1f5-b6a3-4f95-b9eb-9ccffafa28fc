'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { PlusIcon } from 'lucide-react'
import { ClientsTable } from '@/components/clients-table'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import NewClientModal from '@/components/new-client-modal'

export default function ClientsPage() {
  const router = useRouter()
  const [isClientModalOpen, setIsClientModalOpen] = useState(false)

  const handleNewClientClick = () => {
    setIsClientModalOpen(true)
  }

  const handleClientCreated = () => {
    setIsClientModalOpen(false)
    // Refresh the page to show the new client
    router.refresh()
  }

  return (
    <div className="flex flex-col gap-8">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Clients</h1>
        <Button className="flex items-center gap-1" onClick={handleNewClientClick}>
          <PlusIcon className="h-4 w-4" />
          New Client
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search and Filter</CardTitle>
          <CardDescription>
            Find insurance carriers and self-insured organizations
            <span className="ml-2 text-sm italic text-muted-foreground">(Coming soon)</span>
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label htmlFor="client-name" className="text-sm font-medium">
                Name
              </label>
              <Input id="client-name" placeholder="Search by name" disabled />
            </div>
            <div className="space-y-2">
              <label htmlFor="client-prefix" className="text-sm font-medium">
                Prefix
              </label>
              <Input id="client-prefix" placeholder="Search by prefix" disabled />
            </div>
            <div className="space-y-2">
              <label htmlFor="client-type" className="text-sm font-medium">
                Type
              </label>
              <Select disabled>
                <SelectTrigger id="client-type">
                  <SelectValue placeholder="Any Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any Type</SelectItem>
                  <SelectItem value="insurance">Insurance Carrier</SelectItem>
                  <SelectItem value="self-insured">Self-Insured</SelectItem>
                  <SelectItem value="tpa">TPA Client</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label htmlFor="client-status" className="text-sm font-medium">
                Status
              </label>
              <Select disabled>
                <SelectTrigger id="client-status">
                  <SelectValue placeholder="Any Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Any Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="mt-4 flex justify-end">
            <Button disabled>Search Clients</Button>
          </div>
        </CardContent>
      </Card>

      <ClientsTable />

      {/* Client creation modal */}
      {isClientModalOpen && (
        <NewClientModal
          isOpen={isClientModalOpen}
          onClose={() => setIsClientModalOpen(false)}
          onClientCreated={handleClientCreated}
        />
      )}
    </div>
  )
}
