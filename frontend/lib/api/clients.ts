import { ApiClient } from './client'
import { ClientResponse, ClientCreate, ClientUpdate, PaginatedResponse } from './types'

/**
 * Clients API service for handling client-related endpoints
 */
export class ClientsApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get a paginated list of clients with optional filtering
   * @param params - Query parameters for filtering, pagination, and sorting
   */
  async getClients(params?: {
    active_only?: boolean
    page?: number
    size?: number
    sort_by?: string
    sort_order?: 'asc' | 'desc'
  }): Promise<PaginatedResponse<ClientResponse>> {
    const queryParams = new URLSearchParams()

    if (params?.active_only !== undefined) {
      queryParams.append('active_only', String(params.active_only))
    }

    if (params?.page !== undefined) {
      queryParams.append('page', String(params.page))
    }

    if (params?.size !== undefined) {
      queryParams.append('size', String(params.size))
    }

    if (params?.sort_by) {
      queryParams.append('sort_by', params.sort_by)
    }

    if (params?.sort_order) {
      queryParams.append('sort_order', params.sort_order)
    }

    const endpoint = `/api/v1/clients${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.client.request<PaginatedResponse<ClientResponse>>(endpoint)
  }

  /**
   * Get a single client by its UUID
   * @param clientId - The UUID of the client
   */
  async getClientById(clientId: string): Promise<ClientResponse> {
    const endpoint = `/api/v1/clients/${clientId}`
    return this.client.request<ClientResponse>(endpoint)
  }

  /**
   * Create a new client
   * @param clientData - Data for the new client
   */
  async createClient(clientData: ClientCreate): Promise<ClientResponse> {
    return this.client.request<ClientResponse>('/api/v1/clients', {
      method: 'POST',
      body: JSON.stringify(clientData),
    })
  }

  /**
   * Update an existing client
   * @param clientId - The UUID of the client to update
   * @param clientData - The fields to update
   */
  async updateClient(
    clientId: string,
    clientData: ClientUpdate
  ): Promise<ClientResponse> {
    const endpoint = `/api/v1/clients/${clientId}`
    return this.client.request<ClientResponse>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(clientData),
    })
  }

  /**
   * Delete a client
   * @param clientId - The UUID of the client to delete
   */
  async deleteClient(clientId: string): Promise<void> {
    const endpoint = `/api/v1/clients/${clientId}`
    await this.client.request<void>(endpoint, {
      method: 'DELETE',
    })
  }

  // Backward compatibility methods (deprecated - use Client* methods instead)
  async getCustomers(params?: {
    active_only?: boolean
    page?: number
    size?: number
    sort_by?: string
    sort_order?: 'asc' | 'desc'
  }): Promise<PaginatedResponse<ClientResponse>> {
    return this.getClients(params)
  }

  async getCustomerById(customerId: string): Promise<ClientResponse> {
    return this.getClientById(customerId)
  }

  async createCustomer(customerData: ClientCreate): Promise<ClientResponse> {
    return this.createClient(customerData)
  }

  async updateCustomer(
    customerId: string,
    customerData: ClientUpdate
  ): Promise<ClientResponse> {
    return this.updateClient(customerId, customerData)
  }

  async deleteCustomer(customerId: string): Promise<void> {
    return this.deleteClient(customerId)
  }
}

// Backward compatibility export (deprecated - use ClientsApi instead)
export const CustomersApi = ClientsApi
