// Re-export all type definitions for backward compatibility
// These exports will be used by existing code that imports from 'lib/api/types'

// Auth Types
export * from './types/auth-types'

// User Types
export * from './types/user-types'

// Client Types
export * from './types/client-types'

// Core Claim Types
export * from './types/claim-base-types'

// Specific Claim Types
export * from './types/auto-claim-types'
export * from './types/property-claim-types'
export * from './types/gl-claim-types'

// Injury Types
export * from './types/injury-types'

// Property Damage Types
export * from './types/property-damage-types'

// Financial Types
export * from './types/financial-types'

// Document Types
export * from './types/document-types'

// Task Types
export * from './types/task-types'

// Note Types
export * from './types/note-types'

// External Party Types (Witnesses, Attorneys)
export * from './types/external-party-types'

// FNOL Types
export * from './types/fnol-types'

// Report Types
export * from './types/report-types'

// Audit Types
export * from './types/audit-types'

// Common Types
export * from './types/common-types'

// Error Types
export * from './types/error-types'
