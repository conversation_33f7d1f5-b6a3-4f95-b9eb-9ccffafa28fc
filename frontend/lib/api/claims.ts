'use client'

import { ApiClient } from './client'
import {
  <PERSON><PERSON>m,
  ClaimQueryParams,
  PaginatedResponse,
  PaginatedClaimResponse,
  Note,
  NoteCreate,
  NoteUpdate,
  BodilyInjuryDetails,
  BodilyInjuryDetailsUpdate,
  InjuredPerson,
  InjuredPersonCreate,
  InjuredPersonUpdate,
  Injury,
  InjuryCreate,
  InjuryUpdate,
  DamagedPropertyAsset,
  DamagedPropertyAssetCreate,
  DamagedPropertyAssetUpdate,
  DamageInstance,
  DamageInstanceCreate,
  DamageInstanceUpdate,
} from './types'
import { RecoveryDetailsResponse, RecoveryStatus } from './types/claim-base-types'

/**
 * Claims API service for handling claim-related endpoints
 */
export class ClaimsApi {
  private client: ApiClient

  constructor(client: ApiClient) {
    this.client = client
  }

  /**
   * Get a paginated list of claims with optional filters and includes
   */
  async getClaims(
    params?: ClaimQueryParams & { include?: string[] }
  ): Promise<PaginatedResponse<Claim>> {
    const queryParams = new URLSearchParams()
    const includeParams: string[] = []

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (key === 'include' && Array.isArray(value)) {
            // Handle the include array separately
            value.forEach(inc => includeParams.push(inc))
          } else if (key === 'page') {
            // Convert page to skip (0-based)
            const pageNum = Math.max(1, parseInt(value.toString()) || 1) // Ensure page is at least 1
            const size = params.size || 10 // Default size if not provided
            const skip = Math.max(0, (pageNum - 1) * size) // Ensure skip is never negative
            queryParams.append('skip', skip.toString())
          } else if (key === 'size') {
            // Convert size to limit
            queryParams.append('limit', value.toString())
          } else if (key !== 'include') {
            // Append other params as-is
            queryParams.append(key, value.toString())
          }
        }
      })
    }

    // Append include parameters separately
    includeParams.forEach(inc => queryParams.append('include', inc))

    const endpoint = `/api/v1/claims?${queryParams.toString()}`

    // Get the backend paginated response
    const backendResponse = await this.client.request<PaginatedClaimResponse>(endpoint, {
      method: 'GET',
    })

    // Convert backend format (skip/limit) to frontend format (page/size)
    const size = backendResponse.limit
    const page = Math.floor(backendResponse.skip / size) + 1
    const pages = Math.ceil(backendResponse.total / size)

    return {
      items: backendResponse.items,
      total: backendResponse.total,
      page,
      size,
      pages,
    }
  }

  /**
   * Get a claim by ID
   */
  async getClaimById(id: string, includes?: string[]): Promise<Claim> {
    let endpoint = `/api/v1/claims/${id}`
    const queryParams = new URLSearchParams()

    if (includes && includes.length > 0) {
      includes.forEach(inc => queryParams.append('include', inc))
    } else {
      // Default includes for user-friendly display
      queryParams.append('include', 'customer')
      queryParams.append('include', 'assigned_to')
      queryParams.append('include', 'supervisor')
      queryParams.append('include', 'created_by')
    }

    endpoint = `${endpoint}?${queryParams.toString()}`
    return this.client.request<Claim>(endpoint)
  }

  /**
   * Get a claim by claim number
   */
  async getClaimByNumber(claimNumber: string, includes?: string[]): Promise<Claim> {
    let endpoint = `/api/v1/claims/${claimNumber}`
    const queryParams = new URLSearchParams()

    if (includes && includes.length > 0) {
      includes.forEach(inc => queryParams.append('include', inc))
    } else {
      // Default includes for user-friendly display
      queryParams.append('include', 'customer')
      queryParams.append('include', 'assigned_to')
      queryParams.append('include', 'supervisor')
      queryParams.append('include', 'created_by')
    }

    endpoint = `${endpoint}?${queryParams.toString()}`
    return this.client.request<Claim>(endpoint)
  }

  /**
   * Create a new claim
   */
  async createClaim(claimData: Partial<Claim>): Promise<Claim> {
    return this.client.request<Claim>('/api/v1/claims', {
      method: 'POST',
      body: JSON.stringify(claimData),
    })
  }

  /**
   * Update an existing claim
   */
  async updateClaim(id: string, claimData: Partial<Claim>): Promise<Claim> {
    return this.client.request<Claim>(`/api/v1/claims/${id}`, {
      method: 'PATCH',
      body: JSON.stringify(claimData),
    })
  }

  /**
   * Delete a claim
   */
  async deleteClaim(id: string): Promise<void> {
    await this.client.request<void>(`/api/v1/claims/${id}`, {
      method: 'DELETE',
    })
  }

  /**
   * Get claims by client ID
   */
  async getClaimsByClient(
    clientId: string,
    params?: Omit<ClaimQueryParams, 'client_id'>
  ): Promise<PaginatedResponse<Claim>> {
    const queryParams = new URLSearchParams()
    queryParams.append('client_id', clientId)

    if (params?.page !== undefined) {
      queryParams.append('page', String(params.page))
    }
    if (params?.size !== undefined) {
      queryParams.append('size', String(params.size))
    }
    if (params?.sort_by) {
      queryParams.append('sort_by', params.sort_by)
    }
    if (params?.sort_order) {
      queryParams.append('sort_order', params.sort_order)
    }
    if (params?.status) {
      queryParams.append('status', params.status)
    }
    if (params?.claim_type) {
      queryParams.append('claim_type', params.claim_type)
    }
    if (params?.adjuster_id) {
      queryParams.append('adjuster_id', params.adjuster_id)
    }
    if (params?.date_from) {
      queryParams.append('date_from', params.date_from)
    }
    if (params?.date_to) {
      queryParams.append('date_to', params.date_to)
    }

    const endpoint = `/api/v1/claims${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return this.client.request<PaginatedResponse<Claim>>(endpoint)
  }

  /**
   * Get claims by customer ID (deprecated - use getClaimsByClient)
   */
  async getClaimsByCustomer(
    customerId: string,
    params?: Omit<ClaimQueryParams, 'customer_id'>
  ): Promise<PaginatedResponse<Claim>> {
    // For backward compatibility, map to client_id
    return this.getClaimsByClient(customerId, params as Omit<ClaimQueryParams, 'client_id'>)
  }

    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (key === 'page') {
            // Convert page to skip (0-based)
            const pageNum = Math.max(1, parseInt(value.toString()) || 1) // Ensure page is at least 1
            const size = (params as any).size || 10 // Default size if not provided
            const skip = Math.max(0, (pageNum - 1) * size) // Ensure skip is never negative
            queryParams.append('skip', skip.toString())
          } else if (key === 'size') {
            // Convert size to limit
            queryParams.append('limit', value.toString())
          } else {
            // Append other params as-is
            queryParams.append(key, value.toString())
          }
        }
      })
    }

    const endpoint = `/api/v1/claims?${queryParams.toString()}`
    return this.client.request<PaginatedResponse<Claim>>(endpoint)
  }

  /**
   * Update claim status
   */
  async updateClaimStatus(id: string, status: string): Promise<Claim> {
    return this.client.request<Claim>(`/api/v1/claims/${id}/status`, {
      method: 'PUT',
      body: JSON.stringify({ status }),
    })
  }

  /**
   * Get notes for a specific claim
   * @param claimId UUID or claim number
   * @returns Array of notes or empty array if none exists
   */
  async getClaimNotes(claimId: string): Promise<Note[]> {
    try {
      console.log(`Fetching notes for claim ${claimId}`)
      const endpoint = `/api/v1/claims/${claimId}/notes`
      // Adding a timestamp parameter to prevent caching
      const notes = await this.client.request<Note[]>(`${endpoint}?_t=${Date.now()}`)
      console.log(`Successfully fetched notes for claim ${claimId}, count: ${notes.length}`)
      return notes
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return an empty array
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `No notes found for claim ${claimId}, returning empty array. Error: ${error?.message || 'Unknown error'}`
        )
        return []
      }
      // Re-throw other errors
      console.error(`Error fetching notes for claim ${claimId}:`, error)
      throw error
    }
  }

  /**
   * Create a new note for a claim
   * @param claimId UUID or claim number
   */
  async createClaimNote(claimId: string, noteData: NoteCreate): Promise<Note> {
    const endpoint = `/api/v1/claims/${claimId}/notes`
    return this.client.request<Note>(endpoint, {
      method: 'POST',
      body: JSON.stringify({
        ...noteData,
        claim_id: claimId,
      }),
    })
  }

  /**
   * Get a specific note by ID
   */
  async getNote(noteId: string): Promise<Note> {
    const endpoint = `/api/v1/notes/${noteId}`
    return this.client.request<Note>(endpoint)
  }

  /**
   * Update a specific note
   */
  async updateNote(noteId: string, noteData: NoteUpdate): Promise<Note> {
    const endpoint = `/api/v1/notes/${noteId}`
    return this.client.request<Note>(endpoint, {
      method: 'PATCH',
      body: JSON.stringify(noteData),
    })
  }

  /**
   * Delete a specific note
   */
  async deleteNote(noteId: string): Promise<void> {
    const endpoint = `/api/v1/notes/${noteId}`
    await this.client.request<void>(endpoint, {
      method: 'DELETE',
    })
  }

  /**
   * Get bodily injury details for a claim
   * @param claimIdentifier UUID or claim number
   * @returns Bodily injury details or null if none exists
   */
  async getBodilyInjuryDetails(claimIdentifier: string): Promise<BodilyInjuryDetails | null> {
    try {
      console.log(`Fetching bodily injury details for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/bodily_injury`
      // Adding a timestamp parameter to prevent caching
      const details = await this.client.request<BodilyInjuryDetails>(`${endpoint}?_t=${Date.now()}`)
      console.log(`Successfully fetched bodily injury details for claim ${claimIdentifier}`)
      return details
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return null
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `No bodily injury details found for claim ${claimIdentifier}, returning null. Error: ${error?.message || 'Unknown error'}`
        )
        return null
      }
      // Re-throw other errors
      console.error(`Error fetching bodily injury details for claim ${claimIdentifier}:`, error)
      throw error
    }
  }

  /**
   * Update or create bodily injury details for a claim
   * @param claimIdentifier UUID or claim number
   * @param data Bodily injury details to update
   * @returns Updated bodily injury details
   */
  async updateBodilyInjuryDetails(
    claimIdentifier: string,
    data: BodilyInjuryDetailsUpdate
  ): Promise<BodilyInjuryDetails> {
    console.log(`Updating bodily injury details for claim ${claimIdentifier}`)
    const endpoint = `/api/v1/claims/${claimIdentifier}/bodily_injury`
    return this.client.request<BodilyInjuryDetails>(endpoint, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  /**
   * Delete bodily injury details for a claim
   * @param claimIdentifier UUID or claim number
   */
  async deleteBodilyInjuryDetails(claimIdentifier: string): Promise<void> {
    try {
      console.log(`Deleting bodily injury details for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/bodily_injury`
      await this.client.request<void>(endpoint, {
        method: 'DELETE',
      })
      console.log(`Successfully deleted bodily injury details for claim ${claimIdentifier}`)
    } catch (error) {
      console.error(`Error deleting bodily injury details for claim ${claimIdentifier}:`, error)
      throw error
    }
  }

  // Injured Persons API Methods

  /**
   * Get all injured persons for a claim
   * @param claimIdentifier UUID or claim number
   * @returns Array of injured persons or empty array if none exists
   */
  async getInjuredPersons(claimIdentifier: string): Promise<InjuredPerson[]> {
    try {
      console.log(`Fetching injured persons for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons`
      // Adding a timestamp parameter to prevent caching
      const persons = await this.client.request<InjuredPerson[]>(`${endpoint}?_t=${Date.now()}`)
      console.log(
        `Successfully fetched injured persons for claim ${claimIdentifier}, count: ${persons.length}`
      )
      return persons
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return an empty array
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `No injured persons found for claim ${claimIdentifier}, returning empty array. Error: ${error?.message || 'Unknown error'}`
        )
        return []
      }
      // Re-throw other errors
      console.error(`Error fetching injured persons for claim ${claimIdentifier}:`, error)
      throw error
    }
  }

  /**
   * Get a specific injured person for a claim
   * @param claimIdentifier UUID or claim number
   * @param personId UUID of the injured person
   * @returns Injured person or null if not found
   */
  async getInjuredPerson(claimIdentifier: string, personId: string): Promise<InjuredPerson | null> {
    try {
      console.log(`Fetching injured person ${personId} for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons/${personId}`
      // Adding a timestamp parameter to prevent caching
      const person = await this.client.request<InjuredPerson>(`${endpoint}?_t=${Date.now()}`)
      console.log(`Successfully fetched injured person ${personId} for claim ${claimIdentifier}`)
      return person
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return null
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `Injured person ${personId} not found for claim ${claimIdentifier}, returning null. Error: ${error?.message || 'Unknown error'}`
        )
        return null
      }
      // Re-throw other errors
      console.error(
        `Error fetching injured person ${personId} for claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  /**
   * Create a new injured person for a claim
   * @param claimIdentifier UUID or claim number
   * @param personData Data for creating the injured person
   * @returns Created injured person
   */
  async createInjuredPerson(
    claimIdentifier: string,
    personData: InjuredPersonCreate
  ): Promise<InjuredPerson> {
    try {
      console.log(`Creating injured person for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons`
      const person = await this.client.request<InjuredPerson>(endpoint, {
        method: 'POST',
        body: JSON.stringify(personData),
      })
      console.log(`Successfully created injured person ${person.id} for claim ${claimIdentifier}`)
      return person
    } catch (error) {
      console.error(`Error creating injured person for claim ${claimIdentifier}:`, error)
      throw error
    }
  }

  /**
   * Update an injured person for a claim
   * @param claimIdentifier UUID or claim number
   * @param personId UUID of the injured person
   * @param personData Data for updating the injured person
   * @returns Updated injured person
   */
  async updateInjuredPerson(
    claimIdentifier: string,
    personId: string,
    personData: InjuredPersonUpdate
  ): Promise<InjuredPerson> {
    try {
      console.log(`Updating injured person ${personId} for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons/${personId}`
      const person = await this.client.request<InjuredPerson>(endpoint, {
        method: 'PUT',
        body: JSON.stringify(personData),
      })
      console.log(`Successfully updated injured person ${personId} for claim ${claimIdentifier}`)
      return person
    } catch (error) {
      console.error(
        `Error updating injured person ${personId} for claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  /**
   * Delete an injured person from a claim
   * @param claimIdentifier UUID or claim number
   * @param personId UUID of the injured person
   */
  async deleteInjuredPerson(claimIdentifier: string, personId: string): Promise<void> {
    try {
      console.log(`Deleting injured person ${personId} for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons/${personId}`
      await this.client.request<void>(endpoint, {
        method: 'DELETE',
      })
      console.log(`Successfully deleted injured person ${personId} for claim ${claimIdentifier}`)
    } catch (error) {
      console.error(
        `Error deleting injured person ${personId} for claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  // Injuries API Methods

  /**
   * Get all injuries for an injured person
   * @param claimIdentifier UUID or claim number
   * @param personId UUID of the injured person
   * @returns Array of injuries or empty array if none exists
   */
  async getInjuries(claimIdentifier: string, personId: string): Promise<Injury[]> {
    try {
      console.log(`Fetching injuries for person ${personId}, claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons/${personId}/injuries`
      // Adding a timestamp parameter to prevent caching
      const injuries = await this.client.request<Injury[]>(`${endpoint}?_t=${Date.now()}`)
      console.log(`Successfully fetched injuries for person ${personId}, count: ${injuries.length}`)
      return injuries
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return an empty array
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `No injuries found for person ${personId}, returning empty array. Error: ${error?.message || 'Unknown error'}`
        )
        return []
      }
      // Re-throw other errors
      console.error(`Error fetching injuries for person ${personId}:`, error)
      throw error
    }
  }

  /**
   * Get a specific injury
   * @param claimIdentifier UUID or claim number
   * @param personId UUID of the injured person
   * @param injuryId UUID of the injury
   * @returns Injury or null if not found
   */
  async getInjury(
    claimIdentifier: string,
    personId: string,
    injuryId: string
  ): Promise<Injury | null> {
    try {
      console.log(`Fetching injury ${injuryId} for person ${personId}, claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons/${personId}/injuries/${injuryId}`
      // Adding a timestamp parameter to prevent caching
      const injury = await this.client.request<Injury>(`${endpoint}?_t=${Date.now()}`)
      console.log(`Successfully fetched injury ${injuryId}`)
      return injury
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return null
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `Injury ${injuryId} not found, returning null. Error: ${error?.message || 'Unknown error'}`
        )
        return null
      }
      // Re-throw other errors
      console.error(`Error fetching injury ${injuryId}:`, error)
      throw error
    }
  }

  /**
   * Create a new injury for an injured person
   * @param claimIdentifier UUID or claim number
   * @param personId UUID of the injured person
   * @param injuryData Data for creating the injury
   * @returns Created injury
   */
  async createInjury(
    claimIdentifier: string,
    personId: string,
    injuryData: InjuryCreate
  ): Promise<Injury> {
    try {
      console.log(`Creating injury for person ${personId}, claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons/${personId}/injuries`
      const injury = await this.client.request<Injury>(endpoint, {
        method: 'POST',
        body: JSON.stringify(injuryData),
      })
      console.log(`Successfully created injury ${injury.id}`)
      return injury
    } catch (error) {
      console.error(`Error creating injury for person ${personId}:`, error)
      throw error
    }
  }

  /**
   * Update an injury
   * @param claimIdentifier UUID or claim number
   * @param personId UUID of the injured person
   * @param injuryId UUID of the injury
   * @param injuryData Data for updating the injury
   * @returns Updated injury
   */
  async updateInjury(
    claimIdentifier: string,
    personId: string,
    injuryId: string,
    injuryData: InjuryUpdate
  ): Promise<Injury> {
    try {
      console.log(`Updating injury ${injuryId} for person ${personId}, claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons/${personId}/injuries/${injuryId}`
      const injury = await this.client.request<Injury>(endpoint, {
        method: 'PUT',
        body: JSON.stringify(injuryData),
      })
      console.log(`Successfully updated injury ${injuryId}`)
      return injury
    } catch (error) {
      console.error(`Error updating injury ${injuryId}:`, error)
      throw error
    }
  }

  /**
   * Delete an injury
   * @param claimIdentifier UUID or claim number
   * @param personId UUID of the injured person
   * @param injuryId UUID of the injury
   */
  async deleteInjury(claimIdentifier: string, personId: string, injuryId: string): Promise<void> {
    try {
      console.log(`Deleting injury ${injuryId} for person ${personId}, claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/injured-persons/${personId}/injuries/${injuryId}`
      await this.client.request<void>(endpoint, {
        method: 'DELETE',
      })
      console.log(`Successfully deleted injury ${injuryId}`)
    } catch (error) {
      console.error(`Error deleting injury ${injuryId}:`, error)
      throw error
    }
  }

  // Damaged Property Assets API Methods

  /**
   * Get all damaged property assets for a claim
   * @param claimIdentifier UUID or claim number
   * @returns Array of damaged property assets or empty array if none exists
   */
  async getDamagedPropertyAssets(claimIdentifier: string): Promise<DamagedPropertyAsset[]> {
    try {
      console.log(`Fetching damaged property assets for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets`
      // Adding a timestamp parameter to prevent caching
      const assets = await this.client.request<DamagedPropertyAsset[]>(
        `${endpoint}?_t=${Date.now()}`
      )
      console.log(
        `Successfully fetched damaged property assets for claim ${claimIdentifier}, count: ${assets.length}`
      )
      return assets
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return an empty array
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `No damaged property assets found for claim ${claimIdentifier}, returning empty array. Error: ${error?.message || 'Unknown error'}`
        )
        return []
      }
      // Re-throw other errors
      console.error(`Error fetching damaged property assets for claim ${claimIdentifier}:`, error)
      throw error
    }
  }

  /**
   * Get a specific damaged property asset for a claim
   * @param claimIdentifier UUID or claim number
   * @param assetId UUID of the damaged property asset
   * @returns Damaged property asset or null if not found
   */
  async getDamagedPropertyAsset(
    claimIdentifier: string,
    assetId: string
  ): Promise<DamagedPropertyAsset | null> {
    try {
      console.log(`Fetching damaged property asset ${assetId} for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets/${assetId}`
      // Adding a timestamp parameter to prevent caching
      const asset = await this.client.request<DamagedPropertyAsset>(`${endpoint}?_t=${Date.now()}`)
      console.log(
        `Successfully fetched damaged property asset ${assetId} for claim ${claimIdentifier}`
      )
      return asset
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return null
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `Damaged property asset ${assetId} not found for claim ${claimIdentifier}, returning null. Error: ${error?.message || 'Unknown error'}`
        )
        return null
      }
      // Re-throw other errors
      console.error(
        `Error fetching damaged property asset ${assetId} for claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  /**
   * Create a new damaged property asset for a claim
   * @param claimIdentifier UUID or claim number
   * @param assetData Data for creating the damaged property asset
   * @returns Created damaged property asset
   */
  async createDamagedPropertyAsset(
    claimIdentifier: string,
    assetData: DamagedPropertyAssetCreate
  ): Promise<DamagedPropertyAsset> {
    try {
      console.log(`Creating damaged property asset for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets`
      const asset = await this.client.request<DamagedPropertyAsset>(endpoint, {
        method: 'POST',
        body: JSON.stringify(assetData),
      })
      console.log(
        `Successfully created damaged property asset ${asset.id} for claim ${claimIdentifier}`
      )
      return asset
    } catch (error) {
      console.error(`Error creating damaged property asset for claim ${claimIdentifier}:`, error)
      throw error
    }
  }

  /**
   * Update a damaged property asset for a claim
   * @param claimIdentifier UUID or claim number
   * @param assetId UUID of the damaged property asset
   * @param assetData Data for updating the damaged property asset
   * @returns Updated damaged property asset
   */
  async updateDamagedPropertyAsset(
    claimIdentifier: string,
    assetId: string,
    assetData: DamagedPropertyAssetUpdate
  ): Promise<DamagedPropertyAsset> {
    try {
      console.log(`Updating damaged property asset ${assetId} for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets/${assetId}`
      const asset = await this.client.request<DamagedPropertyAsset>(endpoint, {
        method: 'PUT',
        body: JSON.stringify(assetData),
      })
      console.log(
        `Successfully updated damaged property asset ${assetId} for claim ${claimIdentifier}`
      )
      return asset
    } catch (error) {
      console.error(
        `Error updating damaged property asset ${assetId} for claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  /**
   * Delete a damaged property asset for a claim
   * @param claimIdentifier UUID or claim number
   * @param assetId UUID of the damaged property asset
   */
  async deleteDamagedPropertyAsset(claimIdentifier: string, assetId: string): Promise<void> {
    try {
      console.log(`Deleting damaged property asset ${assetId} for claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets/${assetId}`
      await this.client.request<void>(endpoint, {
        method: 'DELETE',
      })
      console.log(
        `Successfully deleted damaged property asset ${assetId} for claim ${claimIdentifier}`
      )
    } catch (error) {
      console.error(
        `Error deleting damaged property asset ${assetId} for claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  // Damage Instances API Methods

  /**
   * Get all damage instances for a damaged property asset
   * @param claimIdentifier UUID or claim number
   * @param assetId UUID of the damaged property asset
   * @returns Array of damage instances or empty array if none exists
   */
  async getDamageInstances(claimIdentifier: string, assetId: string): Promise<DamageInstance[]> {
    try {
      console.log(`Fetching damage instances for asset ${assetId} in claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets/${assetId}/damage-instances`
      // Adding a timestamp parameter to prevent caching
      const instances = await this.client.request<DamageInstance[]>(`${endpoint}?_t=${Date.now()}`)
      console.log(
        `Successfully fetched damage instances for asset ${assetId} in claim ${claimIdentifier}, count: ${instances.length}`
      )
      return instances
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return an empty array
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `No damage instances found for asset ${assetId} in claim ${claimIdentifier}, returning empty array. Error: ${error?.message || 'Unknown error'}`
        )
        return []
      }
      // Re-throw other errors
      console.error(
        `Error fetching damage instances for asset ${assetId} in claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  /**
   * Get a specific damage instance for a damaged property asset
   * @param claimIdentifier UUID or claim number
   * @param assetId UUID of the damaged property asset
   * @param instanceId UUID of the damage instance
   * @returns Damage instance or null if not found
   */
  async getDamageInstance(
    claimIdentifier: string,
    assetId: string,
    instanceId: string
  ): Promise<DamageInstance | null> {
    try {
      console.log(
        `Fetching damage instance ${instanceId} for asset ${assetId} in claim ${claimIdentifier}`
      )
      const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets/${assetId}/damage-instances/${instanceId}`
      // Adding a timestamp parameter to prevent caching
      const instance = await this.client.request<DamageInstance>(`${endpoint}?_t=${Date.now()}`)
      console.log(
        `Successfully fetched damage instance ${instanceId} for asset ${assetId} in claim ${claimIdentifier}`
      )
      return instance
    } catch (error: any) {
      // If the error is a 404 or relates to no content, return null
      if (
        error?.status === 404 ||
        error?.message?.includes('404') ||
        error?.message?.includes('No content') ||
        error?.message?.includes('Invalid response')
      ) {
        console.log(
          `Damage instance ${instanceId} not found for asset ${assetId} in claim ${claimIdentifier}, returning null. Error: ${error?.message || 'Unknown error'}`
        )
        return null
      }
      // Re-throw other errors
      console.error(
        `Error fetching damage instance ${instanceId} for asset ${assetId} in claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  /**
   * Create a new damage instance for a damaged property asset
   * @param claimIdentifier UUID or claim number
   * @param assetId UUID of the damaged property asset
   * @param instanceData Data for creating the damage instance
   * @returns Created damage instance
   */
  async createDamageInstance(
    claimIdentifier: string,
    assetId: string,
    instanceData: DamageInstanceCreate
  ): Promise<DamageInstance> {
    try {
      console.log(`Creating damage instance for asset ${assetId} in claim ${claimIdentifier}`)
      const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets/${assetId}/damage-instances`
      const instance = await this.client.request<DamageInstance>(endpoint, {
        method: 'POST',
        body: JSON.stringify(instanceData),
      })
      console.log(
        `Successfully created damage instance ${instance.id} for asset ${assetId} in claim ${claimIdentifier}`
      )
      return instance
    } catch (error) {
      console.error(
        `Error creating damage instance for asset ${assetId} in claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  /**
   * Update a damage instance for a damaged property asset
   * @param claimIdentifier UUID or claim number
   * @param assetId UUID of the damaged property asset
   * @param instanceId UUID of the damage instance
   * @param instanceData Data for updating the damage instance
   * @returns Updated damage instance
   */
  async updateDamageInstance(
    claimIdentifier: string,
    assetId: string,
    instanceId: string,
    instanceData: DamageInstanceUpdate
  ): Promise<DamageInstance> {
    try {
      console.log(
        `Updating damage instance ${instanceId} for asset ${assetId} in claim ${claimIdentifier}`
      )
      const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets/${assetId}/damage-instances/${instanceId}`
      const instance = await this.client.request<DamageInstance>(endpoint, {
        method: 'PUT',
        body: JSON.stringify(instanceData),
      })
      console.log(
        `Successfully updated damage instance ${instanceId} for asset ${assetId} in claim ${claimIdentifier}`
      )
      return instance
    } catch (error) {
      console.error(
        `Error updating damage instance ${instanceId} for asset ${assetId} in claim ${claimIdentifier}:`,
        error
      )
      throw error
    }
  }

  /**
   * Delete a damage instance for a damaged property asset
   */
  async deleteDamageInstance(
    claimIdentifier: string,
    assetId: string,
    instanceId: string
  ): Promise<void> {
    const endpoint = `/api/v1/claims/${claimIdentifier}/damaged-property-assets/${assetId}/damage-instances/${instanceId}`

    try {
      await this.client.request<void>(endpoint, {
        method: 'DELETE',
      })
    } catch (error) {
      console.error('Error deleting damage instance:', error)
      throw error
    }
  }

  /**
   * Get recovery details for a claim
   */
  async getRecoveryDetails(claimIdentifier: string): Promise<RecoveryDetailsResponse | null> {
    const endpoint = `/api/v1/claims/${claimIdentifier}/recovery`

    try {
      const response = await this.client.request<{
        items: RecoveryDetailsResponse[]
        total: number
      }>(endpoint, {
        method: 'GET',
      })

      // Handle the wrapped response format from the API proxy
      if (response && response.items && response.items.length > 0) {
        return response.items[0]
      }

      return null
    } catch (error) {
      console.error('Error fetching recovery details:', error)
      // Return null if recovery details don't exist (404) or other errors
      return null
    }
  }

  /**
   * Update recovery status for a claim
   */
  async updateRecoveryStatus(
    claimIdentifier: string,
    recoveryStatus: RecoveryStatus
  ): Promise<Claim> {
    const endpoint = `/api/v1/claims/${claimIdentifier}/recovery/status?recovery_status=${recoveryStatus}`

    try {
      return await this.client.request<Claim>(endpoint, {
        method: 'PATCH',
      })
    } catch (error) {
      console.error('Error updating recovery status:', error)
      throw error
    }
  }

  /**
   * Update carrier details for a claim
   */
  async updateCarrierDetails(
    claimIdentifier: string,
    carrierName?: string | null,
    carrierContact?: string | null,
    carrierClaimNumber?: string | null,
    carrierAdjuster?: string | null
  ): Promise<Claim> {
    const queryParams = new URLSearchParams()
    if (carrierName !== undefined) {
      queryParams.append('carrier_name', carrierName || '')
    }
    if (carrierContact !== undefined) {
      queryParams.append('carrier_contact', carrierContact || '')
    }
    if (carrierClaimNumber !== undefined) {
      queryParams.append('carrier_claim_number', carrierClaimNumber || '')
    }
    if (carrierAdjuster !== undefined) {
      queryParams.append('carrier_adjuster', carrierAdjuster || '')
    }

    const endpoint = `/api/v1/claims/${claimIdentifier}/carrier?${queryParams.toString()}`

    try {
      return await this.client.request<Claim>(endpoint, {
        method: 'PATCH',
      })
    } catch (error) {
      console.error('Error updating carrier details:', error)
      throw error
    }
  }

  /**
   * Update recovery amounts for a claim
   */
  async updateRecoveryAmounts(
    claimIdentifier: string,
    expectedAmount?: string | null,
    receivedAmount?: string | null
  ): Promise<Claim> {
    const queryParams = new URLSearchParams()
    if (expectedAmount !== undefined) {
      queryParams.append('expected_amount', expectedAmount || '')
    }
    if (receivedAmount !== undefined) {
      queryParams.append('received_amount', receivedAmount || '')
    }

    const endpoint = `/api/v1/claims/${claimIdentifier}/recovery/amounts?${queryParams.toString()}`

    try {
      return await this.client.request<Claim>(endpoint, {
        method: 'PATCH',
      })
    } catch (error) {
      console.error('Error updating recovery amounts:', error)
      throw error
    }
  }
}
