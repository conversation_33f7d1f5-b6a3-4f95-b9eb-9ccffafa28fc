// Client Types

// Renamed from Customer to ClientResponse to match OpenAPI schema
export interface ClientResponse {
  id: string
  name: string
  prefix: string
  description?: string | null // Add description field, optional
  active: boolean // Add active field
  created_at: string
  updated_at: string
}

// Schema for creating a client
export interface ClientCreate {
  name: string
  prefix: string
  description?: string | null
  active?: boolean // Optional, defaults to true on backend
}

// Schema for updating a client
export interface ClientUpdate {
  name?: string | null
  description?: string | null
  active?: boolean | null
}

// Backward compatibility exports (deprecated - use Client* types instead)
export type CustomerResponse = ClientResponse
export type CustomerCreate = ClientCreate
export type CustomerUpdate = ClientUpdate
