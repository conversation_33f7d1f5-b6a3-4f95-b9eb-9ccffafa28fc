'use client'

import { ApiClient as OriginalApiClient } from './client'
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './claims'
import { ClientsApi } from './clients'
import { TasksApi } from './tasks'
import { DocumentsApi } from './documents'
import { FNOL<PERSON>pi } from './fnol'
import { ReportsApi } from './reports'
import { MetricsApi } from './metrics'
import { UsersApi } from './users'
import { FinancialsApi } from './financials.api'
import { WitnessesA<PERSON> } from './witnesses'
import { AttorneysApi } from './attorneys'
import { AuditApi } from './audit'
// Import other API services as needed

// Export types
export * from './types'

// --- Start of Diagnostic Section ---
console.log('[api/index.ts] TOP LEVEL: Module evaluation starts')

// Re-export ApiClient directly for typing
export type { OriginalApiClient as ApiClientClass } // Exporting the type with correct name

let diagnosticApiClientInstance: OriginalApiClient | null = null

export function getMyUniqueAuthProviderClient(): OriginalApiClient {
  console.log('[api/index.ts] getMyUniqueAuthProviderClient() CALLED')
  if (!diagnosticApiClientInstance) {
    diagnosticApiClientInstance = new OriginalApiClient()
    console.log(
      '[api/index.ts] getMyUniqueAuthProviderClient: NEW OriginalApiClient INSTANCE CREATED'
    )
  } else {
    console.log('[api/index.ts] getMyUniqueAuthProviderClient: REUSING OriginalApiClient INSTANCE')
  }
  if (!diagnosticApiClientInstance) {
    console.error(
      '[api/index.ts] FATAL: diagnosticApiClientInstance is NULL after new OriginalApiClient() or reuse attempt!'
    )
    // Consider throwing an error here if this state is reached, as it's unexpected.
    // throw new Error("Failed to create/get diagnosticApiClientInstance in getMyUniqueAuthProviderClient");
  }
  // Ensure we always return an instance or throw, to satisfy non-nullable return type if changed from OriginalApiClient | null
  if (!diagnosticApiClientInstance)
    throw new Error('diagnosticApiClientInstance is null in getMyUniqueAuthProviderClient')
  return diagnosticApiClientInstance
}
console.log('[api/index.ts] getMyUniqueAuthProviderClient function DEFINED')
// --- End of Diagnostic Section ---

// Shared internal ApiClient instance
let internalApiClientInstance: OriginalApiClient | null = null

function getInternalApiClient(): OriginalApiClient {
  if (!internalApiClientInstance) {
    internalApiClientInstance = new OriginalApiClient()
    console.log('[api/index.ts] Fresh internalApiClientInstance created in getInternalApiClient()')
  }
  // Ensure we always return an instance or throw
  if (!internalApiClientInstance)
    throw new Error('internalApiClientInstance is null in getInternalApiClient')
  return internalApiClientInstance
}

// Define the return type for initializeApiOnce more explicitly
interface ApiObject {
  client: OriginalApiClient
  claims: ClaimsApi
  clients: ClientsApi
  tasks: TasksApi
  documents: DocumentsApi
  fnol: FNOLApi
  reports: ReportsApi
  metrics: MetricsApi
  users: UsersApi
  financials: FinancialsApi
  witnesses: WitnessesApi
  attorneys: AttorneysApi
  audit: AuditApi
}

function initializeApiOnce(): ApiObject {
  console.log('[api/index.ts] initializeApiOnce() called')

  const apiClientToUse = getInternalApiClient()
  console.log(
    '[api/index.ts] apiClientInstance for main api object:',
    apiClientToUse ? 'Exists' : 'Undefined'
  )

  const newApiObject = {
    client: apiClientToUse,
    claims: new ClaimsApi(apiClientToUse),
    clients: new ClientsApi(apiClientToUse),
    tasks: new TasksApi(apiClientToUse),
    documents: new DocumentsApi(apiClientToUse),
    fnol: new FNOLApi(apiClientToUse),
    reports: new ReportsApi(apiClientToUse),
    metrics: new MetricsApi(apiClientToUse),
    users: new UsersApi(apiClientToUse),
    financials: new FinancialsApi(apiClientToUse),
    witnesses: new WitnessesApi(apiClientToUse),
    attorneys: new AttorneysApi(apiClientToUse),
    audit: new AuditApi(apiClientToUse),
  }
  console.log(
    '[api/index.ts] newApiObject.client created:',
    newApiObject.client ? 'Exists' : 'Undefined'
  )
  return newApiObject
}

// Export the initialized api object
// This relies on the module system to ensure initializeApiOnce() is effectively called once.
export const api = initializeApiOnce()
console.log(
  '[api/index.ts] "api" exported. api.client is:',
  api && api.client ? 'Exists' : 'Undefined or api is undefined'
)

// Also export a direct way to get the client instance for AuthProvider diagnosis
export function getApiClientForAuthProvider(): OriginalApiClient {
  console.log('[api/index.ts] getApiClientForAuthProvider called')
  return getInternalApiClient()
}

// Commenting out old individual exports in favor of the composite 'api' object
/*
export {
  // Singleton instances
  apiClient,
  claimsApi,
  customersApi,
  tasksApi,
  documentsApi,
  fnolApi,
  reportsApi,
  metricsApi,
  usersApi,

  // Classes
  ApiClient,
  ClaimsApi,
  CustomersApi,
  TasksApi,
  DocumentsApi,
  FNOLApi,
  ReportsApi,
  MetricsApi,
  UsersApi,
}
*/
